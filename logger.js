const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logsFile = path.join(__dirname, 'activity-logs.json');
    this.logs = [];
    this.loadLogs();
  }

  async loadLogs() {
    try {
      if (fsSync.existsSync(this.logsFile)) {
        const data = await fs.readFile(this.logsFile, 'utf8');
        this.logs = JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load logs:', error);
      this.logs = [];
    }
  }

  async saveLogs() {
    try {
      await fs.writeFile(this.logsFile, JSON.stringify(this.logs, null, 2));
    } catch (error) {
      console.error('Failed to save logs:', error);
    }
  }

  generateLogId() {
    return 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  async logActivity(type, activity, details = {}) {
    const logEntry = {
      id: this.generateLogId(),
      timestamp: new Date().toISOString(),
      type: type, // 'manual', 'scheduled'
      activity: activity, // 'file-processing', 'task-execution'
      status: type === 'manual' ? 'processing' : 'running',
      details: {
        ...details,
        startTime: new Date().toISOString()
      }
    };

    this.logs.unshift(logEntry); // Add to beginning of array
    await this.saveLogs();
    return logEntry.id;
  }

  async createFinalLogEntry(logId, type, activity, status, details = {}) {
    const logEntry = {
      id: logId,
      timestamp: new Date().toISOString(),
      type: type, // 'manual', 'scheduled'
      activity: activity, // 'file-processing', 'task-execution'
      status: status, // 'successful' or 'failed'
      details: {
        ...details,
        endTime: new Date().toISOString()
      }
    };

    this.logs.unshift(logEntry); // Add to beginning of array
    await this.saveLogs();
  }

  async logManualProcessing(folderPath, filterType, fileCount, customPattern = '') {
    return await this.logActivity('manual', 'file-processing', {
      folderPath: folderPath,
      filterType: filterType,
      customPattern: customPattern,
      fileCount: fileCount
    });
  }

  async logScheduledTask(taskName, taskId, folderPath, filterType, fileCount) {
    return await this.logActivity('scheduled', 'task-execution', {
      taskName: taskName,
      taskId: taskId,
      folderPath: folderPath,
      filterType: filterType,
      fileCount: fileCount
    });
  }

  async completeProcessing(logId, type, activity, successCount, errorCount, results = [], additionalDetails = {}) {
    const status = errorCount > 0 ? 'failed' : 'successful';
    await this.createFinalLogEntry(logId, type, activity, status, {
      successCount: successCount,
      errorCount: errorCount,
      totalProcessed: successCount + errorCount,
      results: results,
      ...additionalDetails
    });
  }

  async failActivity(logId, type, activity, errorMessage, additionalDetails = {}) {
    await this.createFinalLogEntry(logId, type, activity, 'failed', {
      errorMessage: errorMessage,
      successCount: 0,
      errorCount: 1,
      totalProcessed: 0,
      ...additionalDetails
    });
  }

  async cancelActivity(logId, additionalDetails = {}) {
    // Find the original log entry to get its type and activity
    const originalLog = this.logs.find(log => log.id === logId);
    if (!originalLog) {
      console.error(`Original log entry with ID ${logId} not found`);
      return;
    }

    await this.createFinalLogEntry(logId, originalLog.type, originalLog.activity, 'cancelled', {
      cancelledAt: new Date().toISOString(),
      successCount: additionalDetails.successCount || 0,
      errorCount: 0,
      totalProcessed: additionalDetails.successCount || 0,
      ...additionalDetails
    });
  }



  async getLogs(limit = 100, type = null) {
    let filteredLogs = this.logs;
    
    if (type) {
      filteredLogs = this.logs.filter(log => log.type === type);
    }

    return filteredLogs.slice(0, limit).map(log => ({
      id: log.id,
      timestamp: log.timestamp,
      type: log.type,
      activity: log.activity,
      status: log.status,
      details: {
        ...log.details,
        // Don't include full results array in summary
        results: log.details.results ? log.details.results.length : 0
      }
    }));
  }

  async getLogDetails(logId) {
    const log = this.logs.find(log => log.id === logId);
    return log || null;
  }

  async clearLogs(olderThanDays = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const originalCount = this.logs.length;
    this.logs = this.logs.filter(log => new Date(log.timestamp) > cutoffDate);

    await this.saveLogs();
    return originalCount - this.logs.length; // Return number of deleted logs
  }

  async clearAllLogs() {
    const originalCount = this.logs.length;
    this.logs = [];

    await this.saveLogs();
    return originalCount; // Return number of deleted logs
  }

  getStats() {
    const stats = {
      total: this.logs.length,
      manual: this.logs.filter(log => log.type === 'manual').length,
      scheduled: this.logs.filter(log => log.type === 'scheduled').length,
      successful: this.logs.filter(log => log.status === 'successful').length,
      failed: this.logs.filter(log => log.status === 'failed').length,
      cancelled: this.logs.filter(log => log.status === 'cancelled').length
    };

    // Calculate success rate
    stats.successRate = stats.total > 0 ? Math.round((stats.successful / stats.total) * 100) : 0;

    return stats;
  }

  formatLogForDisplay(log) {
    const date = new Date(log.timestamp);
    const timeStr = date.toLocaleString();

    let description = '';
    if (log.activity === 'file-processing') {
      if (log.type === 'manual') {
        description = `Manual file processing in ${log.details.folderPath}`;
      } else {
        description = `Scheduled task: ${log.details.taskName}`;
      }
    }

    let statusText = log.status;
    let statusClass = 'status-' + log.status;

    // Add file count to status text
    if (log.status === 'successful' && log.details.successCount !== undefined) {
      statusText = `Successful (${log.details.successCount} files)`;
    } else if (log.status === 'failed') {
      if (log.details.errorMessage) {
        statusText = `Failed (${log.details.errorMessage})`;
      } else {
        statusText = 'Failed';
      }
    } else if (log.status === 'cancelled') {
      const processedCount = log.details.successCount || 0;
      statusText = `Cancelled (${processedCount} files processed)`;
    }

    return {
      id: log.id,
      timestamp: timeStr,
      type: log.type,
      description: description,
      status: statusText,
      statusClass: statusClass,
      details: log.details
    };
  }
}

module.exports = Logger;
