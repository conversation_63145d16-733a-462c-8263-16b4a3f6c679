class AutoRefreshUI {
  constructor() {
    this.selectedFolder = null;
    this.currentFiles = [];
    this.isProcessing = false;
    this.taskRefreshInterval = null;

    this.initializeEventListeners();
    this.loadScheduledTasks();
  }

  initializeEventListeners() {
    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // File Manager events
    document.getElementById('select-folder-btn').addEventListener('click', () => {
      this.selectFolder();
    });

    document.querySelectorAll('input[name="updateMethod"]').forEach(radio => {
      radio.addEventListener('change', () => {
        this.handleUpdateMethodChange();
      });
    });

    // Auto-refresh when dynamic filter inputs change
    document.getElementById('exact-filename').addEventListener('input', () => {
      if (this.selectedFolder && document.querySelector('input[name="updateMethod"]:checked').value === 'exact-filename') {
        clearTimeout(this.customPatternTimeout);
        this.customPatternTimeout = setTimeout(() => {
          this.previewFiles();
        }, 500);
      }
    });

    document.getElementById('time-amount').addEventListener('input', () => {
      if (this.selectedFolder && document.querySelector('input[name="updateMethod"]:checked').value === 'time-based') {
        clearTimeout(this.timeFilterTimeout);
        this.timeFilterTimeout = setTimeout(() => {
          this.previewFiles();
        }, 500);
      }
    });

    document.getElementById('time-unit').addEventListener('change', () => {
      if (this.selectedFolder && document.querySelector('input[name="updateMethod"]:checked').value === 'time-based') {
        this.previewFiles();
      }
    });

    document.getElementById('contains-text').addEventListener('input', () => {
      if (this.selectedFolder && document.querySelector('input[name="updateMethod"]:checked').value === 'contains-text') {
        clearTimeout(this.textFilterTimeout);
        this.textFilterTimeout = setTimeout(() => {
          this.previewFiles();
        }, 500);
      }
    });

    document.getElementById('file-extension').addEventListener('change', () => {
      const extensionSelect = document.getElementById('file-extension');
      const customExtInput = document.getElementById('custom-extension');

      if (extensionSelect.value === 'custom') {
        customExtInput.style.display = 'inline-block';
        customExtInput.focus();
      } else {
        customExtInput.style.display = 'none';
        if (this.selectedFolder && document.querySelector('input[name="updateMethod"]:checked').value === 'by-extension') {
          this.previewFiles();
        }
      }
    });

    document.getElementById('custom-extension').addEventListener('input', () => {
      if (this.selectedFolder && document.querySelector('input[name="updateMethod"]:checked').value === 'by-extension') {
        clearTimeout(this.extensionFilterTimeout);
        this.extensionFilterTimeout = setTimeout(() => {
          this.previewFiles();
        }, 500);
      }
    });



    document.getElementById('process-files-btn').addEventListener('click', () => {
      this.processFiles();
    });

    // Cancel processing button
    document.getElementById('cancel-processing-btn').addEventListener('click', () => {
      this.cancelProcessing();
    });

    // Cancel scheduled task button
    document.getElementById('cancel-scheduler-btn').addEventListener('click', () => {
      this.cancelScheduledTask();
    });



    // Scheduler events
    document.getElementById('task-select-folder-btn').addEventListener('click', () => {
      this.selectTaskFolder();
    });

    // Add event listeners for task update method radio buttons
    document.querySelectorAll('input[name="taskUpdateMethod"]').forEach(radio => {
      radio.addEventListener('change', () => {
        this.handleTaskMethodChange();
        this.validateTaskForm();
      });
    });

    // Auto-refresh task preview when dynamic filter inputs change
    document.getElementById('task-exact-filename').addEventListener('input', () => {
      if (this.selectedTaskFolder && document.querySelector('input[name="taskUpdateMethod"]:checked').value === 'exact-filename') {
        clearTimeout(this.taskCustomPatternTimeout);
        this.taskCustomPatternTimeout = setTimeout(() => {
          this.previewTaskFiles();
        }, 500);
      }
    });

    document.getElementById('task-time-amount').addEventListener('input', () => {
      if (this.selectedTaskFolder && document.querySelector('input[name="taskUpdateMethod"]:checked').value === 'time-based') {
        clearTimeout(this.taskTimeFilterTimeout);
        this.taskTimeFilterTimeout = setTimeout(() => {
          this.previewTaskFiles();
        }, 500);
      }
    });

    document.getElementById('task-time-unit').addEventListener('change', () => {
      if (this.selectedTaskFolder && document.querySelector('input[name="taskUpdateMethod"]:checked').value === 'time-based') {
        this.previewTaskFiles();
      }
    });

    document.getElementById('task-contains-text').addEventListener('input', () => {
      if (this.selectedTaskFolder && document.querySelector('input[name="taskUpdateMethod"]:checked').value === 'contains-text') {
        clearTimeout(this.taskTextFilterTimeout);
        this.taskTextFilterTimeout = setTimeout(() => {
          this.previewTaskFiles();
        }, 500);
      }
    });

    document.getElementById('task-file-extension').addEventListener('change', () => {
      const extensionSelect = document.getElementById('task-file-extension');
      const customExtInput = document.getElementById('task-custom-extension');

      if (extensionSelect.value === 'custom') {
        customExtInput.style.display = 'inline-block';
        customExtInput.focus();
      } else {
        customExtInput.style.display = 'none';
        if (this.selectedTaskFolder && document.querySelector('input[name="taskUpdateMethod"]:checked').value === 'by-extension') {
          this.previewTaskFiles();
        }
      }
    });

    document.getElementById('task-custom-extension').addEventListener('input', () => {
      if (this.selectedTaskFolder && document.querySelector('input[name="taskUpdateMethod"]:checked').value === 'by-extension') {
        clearTimeout(this.taskExtensionFilterTimeout);
        this.taskExtensionFilterTimeout = setTimeout(() => {
          this.previewTaskFiles();
        }, 500);
      }
    });

    // Add validation event listeners for task form
    document.getElementById('task-name').addEventListener('input', () => {
      this.validateTaskForm();
    });

    document.getElementById('task-frequency').addEventListener('change', () => {
      this.validateTaskForm();
    });

    document.getElementById('task-time').addEventListener('change', () => {
      this.validateTaskForm();
    });

    document.getElementById('task-frequency').addEventListener('change', () => {
      this.handleTaskFrequencyChange();
    });

    document.getElementById('create-task-btn').addEventListener('click', () => {
      this.handleTaskSubmit();
    });

    // Settings events
    document.getElementById('startup-checkbox').addEventListener('change', (e) => {
      this.setStartupBehavior(e.target.checked);
    });

    document.getElementById('test-notification-btn').addEventListener('click', () => {
      this.testNotification();
    });

    document.getElementById('debug-tasks-btn').addEventListener('click', () => {
      this.debugScheduledTasks();
    });

    // Logs events
    document.getElementById('refresh-logs-btn').addEventListener('click', () => {
      this.loadLogs();
    });

    document.getElementById('clear-logs-btn').addEventListener('click', () => {
      this.clearAllLogs();
    });

    document.getElementById('log-type-filter').addEventListener('change', () => {
      this.loadLogs();
    });

    document.getElementById('log-status-filter').addEventListener('change', () => {
      this.loadLogs();
    });

    // Progress updates
    window.electronAPI.onProgressUpdate((event, progress) => {
      this.updateProgress(progress);
    });
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');

    // Stop any existing refresh interval
    if (this.taskRefreshInterval) {
      clearInterval(this.taskRefreshInterval);
      this.taskRefreshInterval = null;
    }

    if (tabName === 'scheduler') {
      this.loadScheduledTasks();
      // Start auto-refresh for scheduler tab to show real-time task status
      this.taskRefreshInterval = setInterval(() => {
        this.loadScheduledTasks();
      }, 2000); // Refresh every 2 seconds
    } else if (tabName === 'logs') {
      this.loadLogs();
    } else if (tabName === 'settings') {
      this.loadSettings();
    }
  }

  async selectFolder() {
    try {
      const folderPath = await window.electronAPI.selectFolder();
      if (folderPath) {
        this.selectedFolder = folderPath;
        document.getElementById('selected-folder').textContent = folderPath;

        // Automatically preview all files when folder is selected
        await this.previewFiles();
      }
    } catch (error) {
      this.showError('Failed to select folder: ' + error.message);
    }
  }

  handleUpdateMethodChange() {
    const selectedMethod = document.querySelector('input[name="updateMethod"]:checked').value;
    const exactFilenameInput = document.getElementById('exact-filename-input');

    // Show/hide the exact filename input
    if (selectedMethod === 'exact-filename') {
      exactFilenameInput.style.display = 'block';
    } else {
      exactFilenameInput.style.display = 'none';
    }

    // Automatically refresh file list when method changes (if folder is selected)
    if (this.selectedFolder) {
      this.previewFiles();
    }
  }

  async previewFiles() {
    if (!this.selectedFolder) {
      this.showError('Please select a folder first');
      return;
    }

    try {
      const updateMethod = document.querySelector('input[name="updateMethod"]:checked').value;
      const filterParams = this.getFilterParameters(updateMethod);

      this.currentFiles = await window.electronAPI.getFiles(
        this.selectedFolder,
        updateMethod,
        filterParams
      );

      this.displayFileList(this.currentFiles);
      document.getElementById('process-files-btn').disabled = this.currentFiles.length === 0;
    } catch (error) {
      this.showError('Failed to get files: ' + error.message);
    }
  }

  getFilterParameters(updateMethod) {
    switch (updateMethod) {
      case 'time-based':
        return {
          amount: parseInt(document.getElementById('time-amount').value) || 30,
          unit: document.getElementById('time-unit').value || 'days'
        };

      case 'contains-text':
        return {
          text: document.getElementById('contains-text').value || ''
        };

      case 'by-extension':
        const extensionSelect = document.getElementById('file-extension');
        const customExt = document.getElementById('custom-extension');
        return {
          extension: extensionSelect.value === 'custom' ? customExt.value : extensionSelect.value
        };

      case 'exact-filename':
        return {
          filename: document.getElementById('exact-filename').value || ''
        };

      case 'all':
      default:
        return {};
    }
  }

  displayFileList(files) {
    const fileList = document.getElementById('file-list');
    const selectedMethod = document.querySelector('input[name="updateMethod"]:checked').value;
    const filterParams = this.getFilterParameters(selectedMethod);

    // Get display name for current filter
    const filterDisplayName = this.getFilterDisplayName(selectedMethod, filterParams);

    if (files.length === 0) {
      fileList.innerHTML = `
        <div style="margin-bottom: 1rem; color: #6c757d;">
          <strong>Filter:</strong> ${filterDisplayName}
        </div>
        <p class="placeholder">No files found matching the criteria</p>
      `;
      return;
    }

    const fileItems = files.map(file => `
      <div class="file-item">
        <span class="file-name">${file.name}</span>
        <span class="file-size">${this.formatFileSize(file.size)}</span>
      </div>
    `).join('');

    fileList.innerHTML = `
      <div style="margin-bottom: 1rem;">
        <div style="color: #6c757d; margin-bottom: 0.5rem;">
          <strong>Filter:</strong> ${filterDisplayName}
        </div>
        <div style="font-weight: 500; color: #28a745;">
          Found ${files.length} file(s):
        </div>
      </div>
      ${fileItems}
    `;
  }

  clearFileList() {
    document.getElementById('file-list').innerHTML =
      '<p class="placeholder">Select a folder to automatically preview files</p>';
    document.getElementById('process-files-btn').disabled = true;
  }

  async previewTaskFiles() {
    if (!this.selectedTaskFolder) {
      this.clearTaskFileList();
      return;
    }

    try {
      const updateMethod = document.querySelector('input[name="taskUpdateMethod"]:checked').value;
      const filterParams = this.getTaskFilterParameters(updateMethod);

      this.currentTaskFiles = await window.electronAPI.getFiles(
        this.selectedTaskFolder,
        updateMethod,
        filterParams
      );

      this.displayTaskFileList(this.currentTaskFiles);
    } catch (error) {
      this.showError('Failed to get files: ' + error.message);
    }
  }

  getTaskFilterParameters(updateMethod) {
    switch (updateMethod) {
      case 'time-based':
        return {
          amount: parseInt(document.getElementById('task-time-amount').value) || 30,
          unit: document.getElementById('task-time-unit').value || 'days'
        };

      case 'contains-text':
        return {
          text: document.getElementById('task-contains-text').value || ''
        };

      case 'by-extension':
        const extensionSelect = document.getElementById('task-file-extension');
        const customExt = document.getElementById('task-custom-extension');
        return {
          extension: extensionSelect.value === 'custom' ? customExt.value : extensionSelect.value
        };

      case 'exact-filename':
        return {
          filename: document.getElementById('task-exact-filename').value || ''
        };

      case 'all':
      default:
        return {};
    }
  }

  displayTaskFileList(files) {
    const fileList = document.getElementById('task-file-list');
    const selectedMethod = document.querySelector('input[name="taskUpdateMethod"]:checked').value;
    const filterParams = this.getTaskFilterParameters(selectedMethod);

    // Get display name for current filter
    const filterDisplayName = this.getFilterDisplayName(selectedMethod, filterParams);

    if (files.length === 0) {
      fileList.innerHTML = `
        <div style="margin-bottom: 1rem; color: #6c757d;">
          <strong>Filter:</strong> ${filterDisplayName}
        </div>
        <p class="placeholder">No files found matching the criteria</p>
      `;
      return;
    }

    const fileItems = files.map(file => `
      <div class="file-item">
        <span class="file-name">${file.name}</span>
        <span class="file-size">${file.size}</span>
      </div>
    `).join('');

    fileList.innerHTML = `
      <div style="margin-bottom: 1rem;">
        <div style="color: #6c757d; margin-bottom: 0.5rem;">
          <strong>Filter:</strong> ${filterDisplayName}
        </div>
        <div style="font-weight: 500; color: #28a745;">
          Found ${files.length} file(s):
        </div>
      </div>
      ${fileItems}
    `;
  }

  clearTaskFileList() {
    document.getElementById('task-file-list').innerHTML =
      '<p class="placeholder">Select a folder to automatically preview files</p>';
  }



  async processFiles() {
    if (this.currentFiles.length === 0) {
      this.showError('No files to process');
      return;
    }

    const delay = parseInt(document.getElementById('delay-input').value);
    if (delay < 1 || isNaN(delay)) {
      this.showError('Delay must be at least 1 second');
      return;
    }

    // Get current folder and filter info for logging
    const folderElement = document.getElementById('selected-folder');
    const selectedMethod = document.querySelector('input[name="updateMethod"]:checked').value;
    const filterParams = this.getFilterParameters(selectedMethod);

    const folderPath = folderElement ? folderElement.textContent : 'Unknown';
    const filterType = selectedMethod;
    const filterDescription = this.getFilterDisplayName(selectedMethod, filterParams);

    this.isProcessing = true;
    this.showProgressSection();

    try {
      const result = await window.electronAPI.processFiles(this.currentFiles, delay, folderPath, filterType, filterParams);
      this.handleProcessingResult(result);
    } catch (error) {
      this.showError('Processing failed: ' + error.message);
    } finally {
      this.isProcessing = false;
      this.hideProgressSection();
    }
  }

  showProgressSection() {
    document.getElementById('progress-section').style.display = 'block';
    document.getElementById('process-files-btn').disabled = true;
  }

  hideProgressSection() {
    document.getElementById('progress-section').style.display = 'none';
    document.getElementById('process-files-btn').disabled = false;
  }

  async cancelProcessing() {
    try {
      if (!this.isProcessing) {
        this.showError('No processing is currently running');
        return;
      }

      const result = await window.electronAPI.cancelProcessing();
      if (result.success) {
        console.log('Processing cancellation requested');
        // The progress will be updated via the progress callback when cancellation takes effect
      }
    } catch (error) {
      console.error('Failed to cancel processing:', error);
      this.showError('Failed to cancel processing: ' + error.message);
    }
  }

  async cancelScheduledTask() {
    try {
      const result = await window.electronAPI.cancelScheduledTask();
      if (result.success) {
        console.log('Scheduled task cancellation requested');
        // The progress will be updated via the progress callback when cancellation takes effect
      } else {
        this.showError('No scheduled task is currently running');
      }
    } catch (error) {
      console.error('Failed to cancel scheduled task:', error);
      this.showError('Failed to cancel scheduled task: ' + error.message);
    }
  }

  updateProgress(progress) {
    // Update both File Manager and Scheduler progress sections
    this.updateFileManagerProgress(progress);
    this.updateSchedulerProgress(progress);
  }

  updateFileManagerProgress(progress) {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const currentFile = document.getElementById('current-file');

    if (!progressFill || !progressText || !currentFile) return;

    // Update overall progress percentage
    if (progress.status === 'processing' && progress.stepInfo && progress.stepInfo.phase === 'Complete') {
      // File is complete, update percentage
      progressFill.style.width = progress.percentage + '%';
      progressText.textContent = `${progress.percentage}% (${progress.current}/${progress.total})`;
    } else if (progress.status === 'opening') {
      // Don't update percentage bar yet, just show we're starting
      progressText.textContent = `Processing file ${progress.current}/${progress.total}...`;
    }

    // Update current file with step-by-step information
    if (progress.stepInfo) {
      const stepInfo = progress.stepInfo;
      const countdownText = stepInfo.countdown ? ` (${stepInfo.countdown}s)` : '';

      currentFile.innerHTML = `
        <div style="font-weight: 500; margin-bottom: 0.5rem;">${progress.currentFile}</div>
        <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
          <div style="background: #667eea; color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.8rem; margin-right: 0.75rem;">
            Step ${stepInfo.step}/${stepInfo.totalSteps}
          </div>
          <div style="font-weight: 500; color: #495057;">
            ${stepInfo.phase}
          </div>
        </div>
        <div style="color: #6c757d; font-size: 0.9rem;">
          ${stepInfo.message}${countdownText}
        </div>
        <div style="margin-top: 0.5rem;">
          ${this.renderStepIndicator(stepInfo.step, stepInfo.totalSteps)}
        </div>
      `;
    } else if (progress.step) {
      // Fallback for old format
      currentFile.innerHTML = `
        <div style="font-weight: 500;">${progress.currentFile}</div>
        <div style="font-size: 0.9em; color: #6c757d; margin-top: 0.25rem;">
          ${progress.step}
        </div>
      `;
    } else {
      currentFile.textContent = `Processing: ${progress.currentFile}`;
    }
  }

  renderStepIndicator(currentStep, totalSteps) {
    let indicators = '';
    for (let i = 1; i <= totalSteps; i++) {
      const isActive = i === currentStep;
      const isComplete = i < currentStep;

      let className = 'step-indicator';
      if (isComplete) className += ' complete';
      else if (isActive) className += ' active';

      indicators += `<div class="${className}"></div>`;
    }

    return `<div style="display: flex; gap: 0.25rem;">${indicators}</div>`;
  }



  updateSchedulerProgress(progress) {
    const progressFill = document.getElementById('scheduler-progress-fill');
    const progressText = document.getElementById('scheduler-progress-text');
    const currentFile = document.getElementById('scheduler-current-file');
    const progressSection = document.getElementById('scheduler-progress-section');

    if (!progressFill || !progressText || !currentFile || !progressSection) return;

    // Show progress section when processing starts
    if (progress.status === 'opening' || progress.status === 'processing') {
      progressSection.style.display = 'block';
    }

    // Update overall progress percentage
    if (progress.status === 'processing' && progress.stepInfo && progress.stepInfo.phase === 'Complete') {
      // File is complete, update percentage
      progressFill.style.width = progress.percentage + '%';
      progressText.textContent = `${progress.percentage}% (${progress.current}/${progress.total})`;
    } else if (progress.status === 'opening') {
      // Don't update percentage bar yet, just show we're starting
      progressText.textContent = `Processing file ${progress.current}/${progress.total}...`;
    }

    // Update current file with step-by-step information
    if (progress.stepInfo) {
      const stepInfo = progress.stepInfo;
      const countdownText = stepInfo.countdown ? ` (${stepInfo.countdown}s)` : '';

      currentFile.innerHTML = `
        <div style="font-weight: 500; margin-bottom: 0.5rem;">${progress.currentFile}</div>
        <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
          <div style="background: #667eea; color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.8rem; margin-right: 0.75rem;">
            Step ${stepInfo.step}/${stepInfo.totalSteps}
          </div>
          <div style="font-weight: 500; color: #495057;">
            ${stepInfo.phase}
          </div>
        </div>
        <div style="color: #6c757d; font-size: 0.9rem;">
          ${stepInfo.message}${countdownText}
        </div>
        <div style="margin-top: 0.5rem;">
          ${this.renderStepIndicator(stepInfo.step, stepInfo.totalSteps)}
        </div>
      `;
    } else if (progress.step) {
      // Fallback for old format
      currentFile.innerHTML = `
        <div style="font-weight: 500;">${progress.currentFile}</div>
        <div style="font-size: 0.9em; color: #6c757d; margin-top: 0.25rem;">
          ${progress.step}
        </div>
      `;
    } else {
      currentFile.textContent = `Processing: ${progress.currentFile}`;
    }

    // Hide progress section when processing completes
    if (progress.status === 'completed' || progress.status === 'error' || progress.status === 'cancelled') {
      setTimeout(() => {
        progressSection.style.display = 'none';
        currentFile.textContent = 'No task running';
        progressFill.style.width = '0%';
        progressText.textContent = '0%';
      }, 3000); // Hide after 3 seconds
    }
  }

  handleProcessingResult(result) {
    // Don't show info boxes - notifications will handle completion status
    const successCount = result.results.filter(r => r.status === 'success').length;
    const errorCount = result.results.filter(r => r.status === 'error').length;

    if (result.cancelled) {
      console.log(`Processing cancelled. Processed ${successCount} files before cancellation.`);
    } else {
      console.log(`Processing completed. Success: ${successCount}, Errors: ${errorCount}`);
    }
  }



  // Scheduler methods
  async selectTaskFolder() {
    try {
      const folderPath = await window.electronAPI.selectFolder();
      if (folderPath) {
        this.selectedTaskFolder = folderPath;
        document.getElementById('task-selected-folder').textContent = folderPath;

        // Automatically preview files when folder is selected
        await this.previewTaskFiles();

        // Validate form after folder selection
        this.validateTaskForm();
      }
    } catch (error) {
      this.showError('Failed to select folder: ' + error.message);
    }
  }

  validateTaskForm() {
    const taskName = document.getElementById('task-name').value.trim();
    const folderPath = document.getElementById('task-selected-folder').textContent;
    const frequency = document.getElementById('task-frequency').value;
    const time = document.getElementById('task-time').value;

    const createBtn = document.getElementById('create-task-btn');

    // Check if all required fields are filled
    const isValid = taskName &&
                   folderPath &&
                   folderPath !== 'No folder selected' &&
                   frequency &&
                   time;

    createBtn.disabled = !isValid;
  }

  handleTaskMethodChange() {
    const method = document.querySelector('input[name="taskUpdateMethod"]:checked').value;
    const exactFilenameInput = document.getElementById('task-exact-filename-input');

    // Show/hide the exact filename input
    if (method === 'exact-filename') {
      exactFilenameInput.style.display = 'block';
    } else {
      exactFilenameInput.style.display = 'none';
    }

    // Refresh file preview when method changes
    if (this.selectedTaskFolder) {
      this.previewTaskFiles();
    }
  }

  handleTaskFrequencyChange() {
    const frequency = document.getElementById('task-frequency').value;
    const weeklyInput = document.getElementById('task-weekly-input');
    const monthlyInput = document.getElementById('task-monthly-input');
    const customInput = document.getElementById('task-custom-input');

    // Hide all conditional inputs
    weeklyInput.style.display = 'none';
    monthlyInput.style.display = 'none';
    customInput.style.display = 'none';

    // Show relevant input based on selection
    if (frequency === 'weekly') {
      weeklyInput.style.display = 'block';
    } else if (frequency === 'monthly') {
      monthlyInput.style.display = 'block';
    } else if (frequency === 'custom') {
      customInput.style.display = 'block';
    }
  }

  handleTaskSubmit() {
    // Check if we're in editing mode or creating a new task
    if (this.editingTaskId) {
      this.updateTask();
    } else {
      this.createTask();
    }
  }

  async createTask() {
    // Validate form inputs
    const taskName = document.getElementById('task-name').value.trim();
    const folderPath = document.getElementById('task-selected-folder').textContent;

    if (!taskName) {
      this.showError('Please enter a task name');
      return;
    }

    if (!folderPath || folderPath === 'No folder selected') {
      this.showError('Please select a folder');
      return;
    }

    const frequency = document.getElementById('task-frequency').value;
    const time = document.getElementById('task-time').value;

    // Generate cron expression based on user-friendly inputs
    const cronExpression = this.generateCronExpression(frequency, time);

    const filterType = document.querySelector('input[name="taskUpdateMethod"]:checked').value;
    const filterParams = this.getTaskFilterParameters(filterType);

    const taskConfig = {
      name: taskName,
      folderPath: folderPath,
      filterType: filterType,
      filterParams: filterParams,
      delay: parseInt(document.getElementById('task-delay-input').value),
      frequency: frequency,
      cronExpression: cronExpression,
      scheduleDescription: this.getScheduleDescription(frequency, time)
    };

    if (!taskConfig.name || !taskConfig.folderPath) {
      this.showError('Please fill in all required fields');
      return;
    }

    if (!cronExpression) {
      this.showError('Please configure a valid schedule');
      return;
    }

    try {
      const result = await window.electronAPI.scheduleTask(taskConfig);
      if (result.success) {
        // Task created successfully - no need for alert, just update UI
        this.clearTaskForm();
        this.loadScheduledTasks();
      } else {
        this.showError(result.message);
      }
    } catch (error) {
      this.showError('Failed to create task: ' + error.message);
    }
  }

  generateCronExpression(frequency, time) {
    const [hour, minute] = time.split(':').map(Number);

    switch (frequency) {
      case 'daily':
        return `${minute} ${hour} * * *`;

      case 'weekly':
        const selectedDays = this.getSelectedWeekdays();
        if (selectedDays.length === 0) {
          return null; // No days selected
        }
        return `${minute} ${hour} * * ${selectedDays.join(',')}`;

      case 'monthly':
        const dayOfMonth = document.getElementById('task-day-of-month').value;
        if (dayOfMonth === 'last') {
          // Last day of month - use a different approach
          return `${minute} ${hour} 28-31 * *`; // Approximate for last day
        }
        return `${minute} ${hour} ${dayOfMonth} * *`;

      case 'custom':
        const customMinute = document.getElementById('custom-minute').value;
        const customHour = document.getElementById('custom-hour').value;
        const customDay = document.getElementById('custom-day').value;
        return `${customMinute} ${customHour} * * ${customDay}`;

      default:
        return null;
    }
  }

  getSelectedWeekdays() {
    const days = [];
    const dayCheckboxes = [
      { id: 'day-sunday', value: '0' },
      { id: 'day-monday', value: '1' },
      { id: 'day-tuesday', value: '2' },
      { id: 'day-wednesday', value: '3' },
      { id: 'day-thursday', value: '4' },
      { id: 'day-friday', value: '5' },
      { id: 'day-saturday', value: '6' }
    ];

    dayCheckboxes.forEach(day => {
      const checkbox = document.getElementById(day.id);
      if (checkbox && checkbox.checked) {
        days.push(day.value);
      }
    });

    return days;
  }

  getScheduleDescription(frequency, time) {
    const [hour, minute] = time.split(':').map(Number);
    const timeStr = new Date(2000, 0, 1, hour, minute).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    switch (frequency) {
      case 'daily':
        return `Daily at ${timeStr}`;

      case 'weekly':
        const selectedDays = this.getSelectedWeekdays();
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const dayLabels = selectedDays.map(day => dayNames[parseInt(day)]);
        return `Weekly on ${dayLabels.join(', ')} at ${timeStr}`;

      case 'monthly':
        const dayOfMonth = document.getElementById('task-day-of-month').value;
        const dayText = dayOfMonth === 'last' ? 'last day' : `${dayOfMonth}${this.getOrdinalSuffix(dayOfMonth)}`;
        return `Monthly on ${dayText} at ${timeStr}`;

      case 'custom':
        return `Custom schedule at ${timeStr}`;

      default:
        return 'Unknown schedule';
    }
  }

  getOrdinalSuffix(day) {
    const j = day % 10;
    const k = day % 100;
    if (j == 1 && k != 11) return 'st';
    if (j == 2 && k != 12) return 'nd';
    if (j == 3 && k != 13) return 'rd';
    return 'th';
  }

  clearTaskForm() {
    document.getElementById('task-name').value = '';
    document.getElementById('task-selected-folder').textContent = 'No folder selected';
    document.querySelector('input[name="taskUpdateMethod"][value="all"]').checked = true;

    // Reset dynamic filter inputs
    document.getElementById('task-time-amount').value = '30';
    document.getElementById('task-time-unit').value = 'days';
    document.getElementById('task-contains-text').value = '';
    document.getElementById('task-file-extension').value = '.xlsx';
    document.getElementById('task-custom-extension').value = '';
    document.getElementById('task-custom-extension').style.display = 'none';
    document.getElementById('task-exact-filename').value = '';

    document.getElementById('task-delay-input').value = '5';
    document.getElementById('task-frequency').value = 'daily';
    document.getElementById('task-time').value = '09:00';

    // Reset checkboxes to default (weekdays checked)
    document.getElementById('day-monday').checked = true;
    document.getElementById('day-tuesday').checked = true;
    document.getElementById('day-wednesday').checked = true;
    document.getElementById('day-thursday').checked = true;
    document.getElementById('day-friday').checked = true;
    document.getElementById('day-saturday').checked = false;
    document.getElementById('day-sunday').checked = false;

    // Reset dropdowns
    document.getElementById('task-day-of-month').value = '1';
    document.getElementById('custom-minute').value = '0';
    document.getElementById('custom-hour').value = '9';
    document.getElementById('custom-day').value = '*';

    // Hide conditional inputs
    document.getElementById('task-exact-filename-input').style.display = 'none';
    document.getElementById('task-weekly-input').style.display = 'none';
    document.getElementById('task-monthly-input').style.display = 'none';
    document.getElementById('task-custom-input').style.display = 'none';

    // Clear task folder and file list
    this.selectedTaskFolder = null;
    this.clearTaskFileList();

    // Disable create button
    this.validateTaskForm();
  }

  async loadScheduledTasks() {
    try {
      const tasks = await window.electronAPI.getScheduledTasks();
      this.displayTasks(tasks);
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  }

  displayTasks(tasks) {
    const tasksList = document.getElementById('tasks-list');
    
    if (tasks.length === 0) {
      tasksList.innerHTML = '<p class="placeholder">No scheduled tasks</p>';
      return;
    }

    const taskItems = tasks.map(task => `
      <div class="task-item">
        <div class="task-info">
          <h4>${task.name}</h4>
          <p><strong>Folder:</strong> ${task.folderPath}</p>
          <p><strong>Method:</strong> ${this.getMethodDisplayName(task.filterType)}</p>
          <p><strong>Schedule:</strong> ${task.scheduleDescription || task.frequency}</p>
          <p><strong>Last Run:</strong> ${task.lastRun ? new Date(task.lastRun).toLocaleString() : 'Never'}</p>
          <p><strong>Next Run:</strong> ${task.nextRun ? new Date(task.nextRun).toLocaleString() : 'Calculating...'}</p>
        </div>
        <div class="task-actions">
          ${task.isRunning ?
            `<button class="btn btn-secondary btn-small" disabled title="Task is currently running">
              <span>⏳</span> Running...
            </button>` :
            `<button class="btn btn-primary btn-small" onclick="ui.runTask('${task.id}')" title="Run this task now">
              <span>▶</span> Run Now
            </button>`
          }
          <button class="btn btn-secondary btn-small" onclick="ui.editTask('${task.id}')" title="Edit this task" ${task.isRunning ? 'disabled' : ''}>
            <span>✏️</span> Edit
          </button>
          <button class="btn btn-danger btn-small" onclick="ui.deleteTask('${task.id}')" title="Delete this task" ${task.isRunning ? 'disabled' : ''}>
            <span>🗑</span> Delete
          </button>
        </div>
      </div>
    `).join('');

    tasksList.innerHTML = taskItems;
  }

  async runTask(taskId) {
    try {
      // Show immediate feedback and refresh to show running state
      this.loadScheduledTasks(); // Refresh immediately to show "Running..." state

      // Start the task (this will run in background)
      window.electronAPI.runTask(taskId).then(result => {
        if (result.success) {
          // Don't show success message - notifications will handle this
        } else {
          this.showError(result.message);
        }
        this.loadScheduledTasks(); // Refresh to show updated "Last Run" time and remove running state
      }).catch(error => {
        this.showError('Failed to run task: ' + error.message);
        this.loadScheduledTasks(); // Refresh to remove running state
      });

    } catch (error) {
      this.showError('Failed to run task: ' + error.message);
    }
  }

  async editTask(taskId) {
    try {
      // Get the task details
      const tasks = await window.electronAPI.getScheduledTasks();
      const task = tasks.find(t => t.id === taskId);

      if (!task) {
        this.showError('Task not found');
        return;
      }

      // Populate the form with task data
      document.getElementById('task-name').value = task.name;
      document.getElementById('task-selected-folder').textContent = task.folderPath;
      document.querySelector(`input[name="taskUpdateMethod"][value="${task.filterType}"]`).checked = true;

      // Populate dynamic filter parameters
      if (task.filterParams) {
        switch (task.filterType) {
          case 'time-based':
            document.getElementById('task-time-amount').value = task.filterParams.amount || 30;
            document.getElementById('task-time-unit').value = task.filterParams.unit || 'days';
            break;
          case 'contains-text':
            document.getElementById('task-contains-text').value = task.filterParams.text || '';
            break;
          case 'by-extension':
            if (task.filterParams.extension && ['.xlsx', '.pdf', '.docx', '.txt', '.csv', '.pptx'].includes(task.filterParams.extension)) {
              document.getElementById('task-file-extension').value = task.filterParams.extension;
            } else {
              document.getElementById('task-file-extension').value = 'custom';
              document.getElementById('task-custom-extension').value = task.filterParams.extension || '';
              document.getElementById('task-custom-extension').style.display = 'inline-block';
            }
            break;
          case 'exact-filename':
            document.getElementById('task-exact-filename').value = task.filterParams.filename || '';
            break;
        }
      } else {
        // Legacy support for old tasks with customPattern
        document.getElementById('task-exact-filename').value = task.customPattern || '';
      }

      document.getElementById('task-delay-input').value = task.delay;
      document.getElementById('task-frequency').value = task.frequency;

      // Set the selected task folder and preview files
      this.selectedTaskFolder = task.folderPath;
      await this.previewTaskFiles();

      // Show/hide appropriate inputs based on method and frequency
      this.handleTaskMethodChange();
      this.handleTaskFrequencyChange();

      // Switch to scheduler tab and scroll to form
      this.switchTab('scheduler');
      document.querySelector('#scheduler .section:first-child').scrollIntoView({ behavior: 'smooth' });

      // Store the task ID for updating instead of creating
      this.editingTaskId = taskId;

      // Change button text and show cancel button
      const createBtn = document.getElementById('create-task-btn');
      const cancelBtn = document.getElementById('cancel-edit-btn');
      createBtn.textContent = 'Update Task';
      cancelBtn.style.display = 'inline-block';

      // Validate form to enable button
      this.validateTaskForm();

      // Task loaded for editing - no need for alert, UI changes are sufficient feedback
    } catch (error) {
      this.showError('Failed to load task for editing: ' + error.message);
    }
  }

  async updateTask() {
    if (!this.editingTaskId) {
      this.showError('No task selected for editing');
      return;
    }

    // Validate form inputs
    const taskName = document.getElementById('task-name').value.trim();
    const folderPath = document.getElementById('task-selected-folder').textContent;

    if (!taskName) {
      this.showError('Please enter a task name');
      return;
    }

    if (!folderPath || folderPath === 'No folder selected') {
      this.showError('Please select a folder');
      return;
    }

    const frequency = document.getElementById('task-frequency').value;
    const time = document.getElementById('task-time').value;

    // Generate cron expression based on user-friendly inputs
    const cronExpression = this.generateCronExpression(frequency, time);

    const filterType = document.querySelector('input[name="taskUpdateMethod"]:checked').value;
    const filterParams = this.getTaskFilterParameters(filterType);

    const taskConfig = {
      name: taskName,
      folderPath: folderPath,
      filterType: filterType,
      filterParams: filterParams,
      delay: parseInt(document.getElementById('task-delay-input').value),
      frequency: frequency,
      cronExpression: cronExpression,
      scheduleDescription: this.getScheduleDescription(frequency, time)
    };

    if (!taskConfig.name || !taskConfig.folderPath) {
      this.showError('Please fill in all required fields');
      return;
    }

    if (!cronExpression) {
      this.showError('Please configure a valid schedule');
      return;
    }

    try {
      const result = await window.electronAPI.updateTask(this.editingTaskId, taskConfig);
      if (result.success) {
        // Don't show success message - just update silently
        this.cancelEdit();
        this.loadScheduledTasks();
      } else {
        this.showError(result.message);
      }
    } catch (error) {
      this.showError('Failed to update task: ' + error.message);
    }
  }

  cancelEdit() {
    this.editingTaskId = null;
    this.clearTaskForm();

    // Reset buttons
    const createBtn = document.getElementById('create-task-btn');
    const cancelBtn = document.getElementById('cancel-edit-btn');
    createBtn.textContent = 'Create Task';
    cancelBtn.style.display = 'none';

    // Don't show info message - just cancel silently
  }

  async deleteTask(taskId) {
    if (!confirm('Are you sure you want to delete this task?')) {
      return;
    }

    try {
      const result = await window.electronAPI.deleteTask(taskId);
      if (result.success) {
        // Task deleted successfully - no need for alert, just refresh the list
        this.loadScheduledTasks();
      } else {
        this.showError(result.message);
      }
    } catch (error) {
      this.showError('Failed to delete task: ' + error.message);
    }
  }

  // Utility methods
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getMethodDisplayName(method) {
    const methods = {
      'all': 'All files',
      'current-month': 'Current month',
      'last-2-months': 'Last 2 months',
      'quarter': 'Quarter',
      'custom': 'Exact filename'
    };
    return methods[method] || method;
  }

  getFilterDisplayName(method, filterParams = {}) {
    switch (method) {
      case 'all':
        return 'All files in folder';

      case 'time-based':
        const amount = filterParams.amount || parseInt(document.getElementById('time-amount')?.value) || 30;
        const unit = filterParams.unit || document.getElementById('time-unit')?.value || 'days';
        return `Files from last ${amount} ${unit}`;

      case 'contains-text':
        const text = filterParams.text || document.getElementById('contains-text')?.value || '';
        return text ? `Files containing "${text}"` : 'Files containing text';

      case 'by-extension':
        const extension = filterParams.extension || this.getCurrentExtension();
        return extension ? `Files with extension ${extension}` : 'Files by extension';

      case 'exact-filename':
        const filename = filterParams.filename || document.getElementById('exact-filename')?.value || '';
        return filename ? `Exact filename: "${filename}"` : 'Exact filename match';

      default:
        return method;
    }
  }

  getCurrentExtension() {
    const extensionSelect = document.getElementById('file-extension');
    const customExt = document.getElementById('custom-extension');
    if (!extensionSelect) return '';

    return extensionSelect.value === 'custom' ? customExt?.value || '' : extensionSelect.value;
  }

  showError(message) {
    alert('Error: ' + message);
  }

  showSuccess(message) {
    alert('Success: ' + message);
  }

  showInfo(message) {
    alert('Info: ' + message);
  }

  // Settings methods
  async loadSettings() {
    try {
      // Load startup status
      const startupStatus = await window.electronAPI.getStartupStatus();
      document.getElementById('startup-checkbox').checked = startupStatus.enabled;



    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async setStartupBehavior(enabled) {
    try {
      const result = await window.electronAPI.setStartup(enabled);
      if (result.success) {
        // Startup behavior changed successfully - no need for alert, checkbox state is sufficient feedback
        console.log(`Startup behavior ${enabled ? 'enabled' : 'disabled'}`);
      } else {
        this.showError(`Failed to change startup behavior: ${result.error}`);
        // Revert checkbox state
        document.getElementById('startup-checkbox').checked = !enabled;
      }
    } catch (error) {
      this.showError('Failed to change startup behavior: ' + error.message);
      // Revert checkbox state
      document.getElementById('startup-checkbox').checked = !enabled;
    }
  }

  async testNotification() {
    try {
      await window.electronAPI.showNotification(
        'Test Notification',
        'This is a test notification from AutoRefresh!'
      );
    } catch (error) {
      this.showError('Failed to show notification: ' + error.message);
    }
  }

  async debugScheduledTasks() {
    try {
      await window.electronAPI.debugScheduledTasks();
      console.log('Debug information logged to console. Check the developer console (F12) for details.');
    } catch (error) {
      this.showError('Failed to debug scheduled tasks: ' + error.message);
    }
  }

  // Logs methods
  async loadLogs() {
    try {
      const typeFilter = document.getElementById('log-type-filter').value;
      const statusFilter = document.getElementById('log-status-filter').value;

      // Load logs and stats
      const [logs, stats] = await Promise.all([
        window.electronAPI.getLogs(100, typeFilter || null),
        window.electronAPI.getLogStats()
      ]);

      // Update stats display
      this.updateLogStats(stats);

      // Filter by status if needed (using partial matching)
      let filteredLogs = logs;
      if (statusFilter) {
        filteredLogs = logs.filter(log => {
          const logStatus = log.status || '';
          return logStatus.toLowerCase().includes(statusFilter.toLowerCase());
        });
      }

      // Display logs
      this.displayLogs(filteredLogs);

    } catch (error) {
      console.error('Failed to load logs:', error);
      this.showError('Failed to load logs: ' + error.message);
    }
  }

  updateLogStats(stats) {
    document.getElementById('stat-total').textContent = stats.total;
    document.getElementById('stat-success-rate').textContent = stats.successRate + '%';
    document.getElementById('stat-manual').textContent = stats.manual;
    document.getElementById('stat-scheduled').textContent = stats.scheduled;
  }

  displayLogs(logs) {
    const logsList = document.getElementById('logs-list');

    if (logs.length === 0) {
      logsList.innerHTML = '<p class="placeholder">No activity logs found</p>';
      return;
    }

    const logItems = logs.map(log => {
      const timestamp = new Date(log.timestamp).toLocaleString();
      const typeClass = `type-${log.type}`;
      const statusClass = `status-${log.status.replace('-', '_')}`;

      let description = '';
      if (log.details.taskName) {
        description = `Task: ${log.details.taskName}`;
      } else {
        description = `Manual processing`;
      }

      if (log.details.folderPath) {
        const folderName = log.details.folderPath.split(/[/\\]/).pop();
        description += ` in ${folderName}`;
      }

      let statusText = log.status;
      if (log.details.successCount !== undefined) {
        statusText += ` (${log.details.successCount} files)`;
        if (log.details.errorCount > 0) {
          statusText += ` ${log.details.errorCount} errors`;
        }
      }

      return `
        <div class="log-item" onclick="ui.showLogDetails('${log.id}')">
          <div class="log-info">
            <h4>
              <span class="log-type ${typeClass}">${log.type}</span>
              ${description}
            </h4>
            <p class="log-timestamp">${timestamp}</p>
            <p>Filter: ${log.details.filterType || 'all'} | Files: ${log.details.fileCount || 0}</p>
            ${log.details.duration ? `<p>Duration: ${log.details.duration}s</p>` : ''}
          </div>
          <div class="log-status ${statusClass}">
            ${statusText}
          </div>
        </div>
      `;
    }).join('');

    logsList.innerHTML = logItems;
  }

  async showLogDetails(logId) {
    try {
      const logDetails = await window.electronAPI.getLogDetails(logId);
      if (logDetails) {
        console.log('Log Details:', logDetails);
        // Could implement a modal or expandable section instead of alert
        // For now, just log to console
      }
    } catch (error) {
      this.showError('Failed to load log details: ' + error.message);
    }
  }

  async clearAllLogs() {
    if (!confirm('Clear all logs? This cannot be undone.')) {
      return;
    }

    try {
      const deletedCount = await window.electronAPI.clearAllLogs();
      console.log(`Cleared ${deletedCount} log entries`);
      this.loadLogs(); // Refresh the display
    } catch (error) {
      this.showError('Failed to clear all logs: ' + error.message);
    }
  }
}

// Initialize the UI when the page loads
const ui = new AutoRefreshUI();
