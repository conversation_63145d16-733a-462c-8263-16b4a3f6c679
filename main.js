const { app, BrowserWindow, ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const FileManager = require('./file-manager');
const Scheduler = require('./scheduler');
const Logger = require('./logger');

class AutoRefreshApp {
  constructor() {
    this.mainWindow = null;
    this.fileManager = new FileManager();
    this.scheduler = new Scheduler();
    this.logger = new Logger();
    this.isDev = process.argv.includes('--dev');
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, 'assets', 'icon.png'),
      show: false
    });

    this.mainWindow.loadFile('index.html');

    if (this.isDev) {
      this.mainWindow.webContents.openDevTools();
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  setupIpcHandlers() {
    // File operations
    ipcMain.handle('select-folder', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openDirectory']
      });
      return result.canceled ? null : result.filePaths[0];
    });

    ipcMain.handle('get-files', async (event, folderPath, filterType, filterParams) => {
      return await this.fileManager.getFiles(folderPath, filterType, filterParams);
    });

    ipcMain.handle('process-files', async (event, files, delay, folderPath, filterType, filterParams) => {
      // Show start notification
      this.showNotification('AutoRefresh Started', `Processing ${files.length} files...`);

      const result = await this.fileManager.processFiles(files, delay, (progress) => {
        this.mainWindow.webContents.send('progress-update', progress);
      }, folderPath, filterType, filterParams);

      // Show completion notification
      const successCount = result.results.filter(r => r.status === 'success').length;
      const errorCount = result.results.filter(r => r.status === 'error').length;

      if (result.cancelled) {
        this.showNotification('AutoRefresh Cancelled', 'File processing was cancelled by user');
      } else {
        this.showNotification('AutoRefresh Completed',
          `Processed ${successCount} files successfully${errorCount > 0 ? `, ${errorCount} errors` : ''}`);
      }

      return result;
    });

    ipcMain.handle('cancel-processing', async () => {
      this.fileManager.cancelProcessing();
      return { success: true };
    });



    // Scheduling operations
    ipcMain.handle('schedule-task', async (event, taskConfig) => {
      return await this.scheduler.scheduleTask(taskConfig);
    });

    ipcMain.handle('get-scheduled-tasks', async () => {
      return await this.scheduler.getTasks();
    });

    ipcMain.handle('run-task', async (event, taskId) => {
      try {
        await this.scheduler.executeTask(taskId);
        return { success: true };
      } catch (error) {
        return { success: false, message: error.message };
      }
    });

    ipcMain.handle('update-task', async (event, taskId, taskConfig) => {
      return await this.scheduler.updateTask(taskId, taskConfig);
    });

    ipcMain.handle('delete-task', async (event, taskId) => {
      return await this.scheduler.deleteTask(taskId);
    });

    ipcMain.handle('cancel-scheduled-task', async () => {
      const cancelled = this.scheduler.cancelCurrentTask();
      return { success: cancelled };
    });

    // Utility operations
    ipcMain.handle('open-file', async (event, filePath) => {
      return await shell.openPath(filePath);
    });

    ipcMain.handle('show-item-in-folder', async (event, filePath) => {
      return shell.showItemInFolder(filePath);
    });

    // Startup operations
    ipcMain.handle('set-startup', async (event, enabled) => {
      return await this.setStartupBehavior(enabled);
    });

    ipcMain.handle('get-startup-status', async () => {
      return await this.getStartupStatus();
    });

    // Notification operations
    ipcMain.handle('show-notification', async (event, title, body) => {
      this.showNotification(title, body);
    });

    // Logging operations
    ipcMain.handle('get-logs', async (event, limit, type) => {
      return await this.logger.getLogs(limit, type);
    });

    ipcMain.handle('get-log-details', async (event, logId) => {
      return await this.logger.getLogDetails(logId);
    });

    ipcMain.handle('get-log-stats', async () => {
      return this.logger.getStats();
    });

    ipcMain.handle('clear-old-logs', async (event, olderThanDays) => {
      return await this.logger.clearLogs(olderThanDays);
    });

    ipcMain.handle('clear-all-logs', async (event) => {
      return await this.logger.clearAllLogs();
    });

    // Debug handler for scheduled tasks
    ipcMain.handle('debug-scheduled-tasks', async () => {
      this.scheduler.debugScheduledTasks();
      return { success: true };
    });
  }

  showNotification(title, body) {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: title,
        body: body,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        silent: false
      });

      notification.show();

      // Auto-close notification after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);
    }
  }

  async setStartupBehavior(enabled) {
    try {
      if (enabled) {
        app.setLoginItemSettings({
          openAtLogin: true,
          path: process.execPath,
          args: ['--startup']
        });
      } else {
        app.setLoginItemSettings({
          openAtLogin: false
        });
      }
      return { success: true, enabled: enabled };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async getStartupStatus() {
    try {
      const settings = app.getLoginItemSettings();
      return { enabled: settings.openAtLogin };
    } catch (error) {
      return { enabled: false, error: error.message };
    }
  }

  init() {
    app.whenReady().then(() => {
      this.createWindow();
      this.setupIpcHandlers();

      // Connect scheduler notifications and progress
      this.scheduler.setNotificationCallback((title, body) => {
        this.showNotification(title, body);
      });

      this.scheduler.setProgressCallback((progress) => {
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send('progress-update', progress);
        }
      });

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', () => {
      this.scheduler.cleanup();
    });
  }
}

const autoRefreshApp = new AutoRefreshApp();
autoRefreshApp.init();
