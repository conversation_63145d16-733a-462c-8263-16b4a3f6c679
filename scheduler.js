const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const cron = require('node-cron');
const FileManager = require('./file-manager');
const Logger = require('./logger');

class Scheduler {
  constructor() {
    this.tasks = new Map();
    this.fileManager = new FileManager();
    this.logger = new Logger();
    this.tasksFile = path.join(__dirname, 'scheduled-tasks.json');
    this.notificationCallback = null;
    this.progressCallback = null;
    this.runningTasks = new Set(); // Track currently running tasks
    this.currentTaskId = null; // Track the currently executing task

    this.loadTasks();
  }



  setNotificationCallback(callback) {
    this.notificationCallback = callback;
  }

  setProgressCallback(callback) {
    this.progressCallback = callback;
  }

  showNotification(title, body) {
    if (this.notificationCallback) {
      this.notificationCallback(title, body);
    }
  }

  async loadTasks() {
    try {
      if (fsSync.existsSync(this.tasksFile)) {
        const data = await fs.readFile(this.tasksFile, 'utf8');
        const tasksData = JSON.parse(data);

        for (const taskData of tasksData) {
          await this.restoreTask(taskData);
        }
      }
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  }

  async saveTasks() {
    try {
      const tasksData = Array.from(this.tasks.values()).map(task => ({
        id: task.id,
        name: task.name,
        folderPath: task.folderPath,
        filterType: task.filterType,
        customPattern: task.customPattern,
        delay: task.delay,
        frequency: task.frequency,
        cronExpression: task.cronExpression,
        enabled: task.enabled,
        created: task.created,
        lastRun: task.lastRun
      }));

      await fs.writeFile(this.tasksFile, JSON.stringify(tasksData, null, 2));
    } catch (error) {
      console.error('Failed to save tasks:', error);
    }
  }

  async restoreTask(taskData) {
    try {
      const task = {
        ...taskData,
        cronJob: null
      };

      if (task.enabled && task.cronExpression) {
        this.scheduleTaskExecution(task);
      }

      this.tasks.set(task.id, task);
    } catch (error) {
      console.error(`Failed to restore task ${taskData.id}:`, error);
    }
  }

  getFrequencyInterval(frequency) {
    switch (frequency) {
      case 'daily':
        return 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      case 'weekly':
        return 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
      case 'monthly':
        return 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
      case 'hourly':
        return 60 * 60 * 1000; // 1 hour in milliseconds
      default:
        return 24 * 60 * 60 * 1000; // Default to daily
    }
  }

  scheduleTaskExecution(task) {
    if (!task.cronExpression) {
      console.error(`Task ${task.name} has no cron expression, cannot schedule`);
      return;
    }

    console.log(`Scheduling task "${task.name}" with cron expression: ${task.cronExpression}`);

    try {
      // Validate cron expression
      if (!cron.validate(task.cronExpression)) {
        console.error(`Invalid cron expression for task ${task.name}: ${task.cronExpression}`);
        return;
      }

      // Schedule the task using node-cron
      task.cronJob = cron.schedule(task.cronExpression, () => {
        console.log(`Cron triggered for task: ${task.name} at ${new Date().toISOString()}`);
        this.executeTask(task.id);
      }, {
        scheduled: true,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      });

      console.log(`Task "${task.name}" scheduled successfully. Next run: ${this.getNextRunTime(task)}`);

      // Add debugging: log when the cron job is created
      console.log(`Cron job status for "${task.name}": running=${task.cronJob.running}, scheduled=${task.cronJob.scheduled}`);
    } catch (error) {
      console.error(`Failed to schedule task ${task.name}:`, error);
    }
  }

  // Debug method to check all scheduled tasks
  debugScheduledTasks() {
    console.log('=== SCHEDULED TASKS DEBUG ===');
    for (const [taskId, task] of this.tasks) {
      console.log(`Task: ${task.name}`);
      console.log(`  ID: ${taskId}`);
      console.log(`  Enabled: ${task.enabled}`);
      console.log(`  Cron Expression: ${task.cronExpression}`);
      console.log(`  Has Cron Job: ${!!task.cronJob}`);
      if (task.cronJob) {
        console.log(`  Cron Job Running: ${task.cronJob.running}`);
        console.log(`  Cron Job Scheduled: ${task.cronJob.scheduled}`);
      }
      console.log(`  Next Run: ${this.getNextRunTime(task)}`);
      console.log('---');
    }
    console.log('=== END DEBUG ===');
  }

  async scheduleTask(taskConfig) {
    try {
      const taskId = this.generateTaskId();

      const task = {
        id: taskId,
        name: taskConfig.name,
        folderPath: taskConfig.folderPath,
        filterType: taskConfig.filterType,
        customPattern: taskConfig.customPattern || '',
        delay: taskConfig.delay,
        frequency: taskConfig.frequency,
        cronExpression: taskConfig.cronExpression,
        scheduleDescription: taskConfig.scheduleDescription || taskConfig.frequency,
        enabled: true,
        created: new Date().toISOString(),
        lastRun: null,
        cronJob: null
      };

      // Schedule the task
      this.scheduleTaskExecution(task);
      this.tasks.set(taskId, task);
      await this.saveTasks();

      return {
        success: true,
        taskId: taskId,
        message: 'Task scheduled successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  }

  async executeTask(taskId) {
    const task = this.tasks.get(taskId);
    if (!task) {
      console.error(`Task ${taskId} not found`);
      return;
    }

    // Check if task is already running
    if (this.runningTasks.has(taskId)) {
      console.log(`Task ${task.name} is already running, skipping...`);
      return;
    }

    // Mark task as running
    this.runningTasks.add(taskId);
    this.currentTaskId = taskId;

    let logId = null;

    try {
      console.log(`Executing task: ${task.name}`);

      // Get files based on task configuration
      const files = await this.fileManager.getFiles(
        task.folderPath,
        task.filterType,
        task.customPattern
      );

      if (files.length === 0) {
        console.log(`No files found for task: ${task.name}`);
        this.showNotification('Scheduled Task', `No files found for task "${task.name}"`);

        // Log the no-files result
        logId = await this.logger.logScheduledTask(task.name, taskId, task.folderPath, task.filterType, 0);
        await this.logger.completeProcessing(logId, 0, 0, []);
        return;
      }

      // Start logging
      logId = await this.logger.logScheduledTask(task.name, taskId, task.folderPath, task.filterType, files.length);

      // Show start notification
      this.showNotification('Scheduled Task Started', `"${task.name}" - Processing ${files.length} files`);

      // Process files with progress callback
      const result = await this.fileManager.processFiles(files, task.delay, (progress) => {
        console.log(`Task ${task.name} progress: ${progress.percentage}%`);
        // Send progress to main window if it exists
        if (this.progressCallback) {
          this.progressCallback(progress);
        }
      }, task.folderPath, task.filterType, task.customPattern);

      // Update last run time
      task.lastRun = new Date().toISOString();
      await this.saveTasks();

      // Complete logging
      const successCount = result.results.filter(r => r.status === 'success').length;
      const errorCount = result.results.filter(r => r.status === 'error').length;

      if (result.cancelled) {
        await this.logger.cancelActivity(logId, {
          successCount: successCount,
          processedFiles: result.results.length,
          taskName: task.name
        });
        this.showNotification('Scheduled Task Cancelled', `"${task.name}" was cancelled`);
      } else {
        await this.logger.completeProcessing(logId, successCount, errorCount, result.results);
        this.showNotification('Scheduled Task Completed',
          `"${task.name}" - Processed ${successCount} files${errorCount > 0 ? `, ${errorCount} errors` : ''}`);
      }

      console.log(`Task ${task.name} completed. Processed ${result.results.length} files.`);
    } catch (error) {
      console.error(`Failed to execute task ${task.name}:`, error);

      // Log the error
      if (logId) {
        await this.logger.failActivity(logId, error.message);
      }

      this.showNotification('Scheduled Task Error', `"${task.name}" failed: ${error.message}`);
    } finally {
      // Always remove task from running tasks
      this.runningTasks.delete(taskId);
      this.currentTaskId = null;
    }
  }

  cancelCurrentTask() {
    if (this.currentTaskId) {
      console.log(`Cancelling current task: ${this.currentTaskId}`);
      this.fileManager.cancelProcessing();
      return true;
    }
    return false;
  }

  async getTasks() {
    return Array.from(this.tasks.values()).map(task => ({
      id: task.id,
      name: task.name,
      folderPath: task.folderPath,
      filterType: task.filterType,
      customPattern: task.customPattern,
      delay: task.delay,
      frequency: task.frequency,
      cronExpression: task.cronExpression,
      scheduleDescription: task.scheduleDescription || task.frequency,
      enabled: task.enabled,
      created: task.created,
      lastRun: task.lastRun,
      nextRun: this.getNextRunTime(task),
      isRunning: this.runningTasks.has(task.id)
    }));
  }

  isAnyTaskRunning() {
    return this.runningTasks.size > 0;
  }

  getNextRunTime(task) {
    if (!task.cronExpression || !task.cronJob) {
      return null;
    }

    try {
      // Use node-cron to get the next execution time
      const cronParser = require('node-cron');

      // Parse the cron expression to calculate next run
      const [minute, hour, dayOfMonth, month, dayOfWeek] = task.cronExpression.split(' ');
      const now = new Date();
      const nextRun = new Date(now);

      // Set the time based on cron expression
      nextRun.setMinutes(parseInt(minute));
      nextRun.setHours(parseInt(hour));
      nextRun.setSeconds(0);
      nextRun.setMilliseconds(0);

      // If the time has already passed today, move to next occurrence
      if (nextRun <= now) {
        if (task.frequency === 'daily') {
          nextRun.setDate(nextRun.getDate() + 1);
        } else if (task.frequency === 'weekly') {
          nextRun.setDate(nextRun.getDate() + 7);
        } else if (task.frequency === 'monthly') {
          nextRun.setMonth(nextRun.getMonth() + 1);
        }
      }

      return nextRun.toISOString();
    } catch (error) {
      console.error(`Error calculating next run time for task ${task.name}:`, error);
      return null;
    }
  }

  async deleteTask(taskId) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Stop the cron job
      if (task.cronJob) {
        task.cronJob.stop();
        if (typeof task.cronJob.destroy === 'function') {
          task.cronJob.destroy();
        }
      }

      this.tasks.delete(taskId);
      await this.saveTasks();

      return {
        success: true,
        message: 'Task deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  }

  async updateTask(taskId, taskConfig) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Stop existing cron job
      if (task.cronJob) {
        task.cronJob.stop();
        if (typeof task.cronJob.destroy === 'function') {
          task.cronJob.destroy();
        }
      }

      // Update task configuration
      Object.assign(task, {
        name: taskConfig.name,
        folderPath: taskConfig.folderPath,
        filterType: taskConfig.filterType,
        customPattern: taskConfig.customPattern || '',
        delay: taskConfig.delay,
        frequency: taskConfig.frequency,
        cronExpression: taskConfig.cronExpression,
        scheduleDescription: taskConfig.scheduleDescription || taskConfig.frequency
      });

      // Create new cron job
      if (task.enabled) {
        this.scheduleTaskExecution(task);
      }

      await this.saveTasks();

      return {
        success: true,
        message: 'Task updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  }

  generateTaskId() {
    return 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  cleanup() {
    // Stop all cron jobs
    for (const task of this.tasks.values()) {
      if (task.cronJob) {
        task.cronJob.stop();
        if (typeof task.cronJob.destroy === 'function') {
          task.cronJob.destroy();
        }
      }
    }
  }
}

module.exports = Scheduler;
