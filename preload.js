const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  getFiles: (folderPath, filterType, customPattern) => 
    ipcRenderer.invoke('get-files', folderPath, filterType, customPattern),
  processFiles: (files, delay, folderPath, filterType, customPattern) =>
    ipcRenderer.invoke('process-files', files, delay, folderPath, filterType, customPattern),
  cancelProcessing: () =>
    ipcRenderer.invoke('cancel-processing'),

  
  // Progress updates
  onProgressUpdate: (callback) => 
    ipcRenderer.on('progress-update', callback),
  removeProgressListener: (callback) => 
    ipcRenderer.removeListener('progress-update', callback),
  
  // Scheduling operations
  scheduleTask: (taskConfig) => 
    ipcRenderer.invoke('schedule-task', taskConfig),
  getScheduledTasks: () =>
    ipcRenderer.invoke('get-scheduled-tasks'),
  runTask: (taskId) =>
    ipcRenderer.invoke('run-task', taskId),
  updateTask: (taskId, taskConfig) =>
    ipcRenderer.invoke('update-task', taskId, taskConfig),
  deleteTask: (taskId) =>
    ipcRenderer.invoke('delete-task', taskId),
  cancelScheduledTask: () =>
    ipcRenderer.invoke('cancel-scheduled-task'),
  
  // Utility operations
  openFile: (filePath) =>
    ipcRenderer.invoke('open-file', filePath),
  showItemInFolder: (filePath) =>
    ipcRenderer.invoke('show-item-in-folder', filePath),

  // Startup operations
  setStartup: (enabled) =>
    ipcRenderer.invoke('set-startup', enabled),
  getStartupStatus: () =>
    ipcRenderer.invoke('get-startup-status'),

  // Notification operations
  showNotification: (title, body) =>
    ipcRenderer.invoke('show-notification', title, body),

  // Logging operations
  getLogs: (limit, type) =>
    ipcRenderer.invoke('get-logs', limit, type),
  getLogDetails: (logId) =>
    ipcRenderer.invoke('get-log-details', logId),
  getLogStats: () =>
    ipcRenderer.invoke('get-log-stats'),
  clearOldLogs: (olderThanDays) =>
    ipcRenderer.invoke('clear-old-logs', olderThanDays),
  clearAllLogs: () =>
    ipcRenderer.invoke('clear-all-logs'),

  // Debug operations
  debugScheduledTasks: () =>
    ipcRenderer.invoke('debug-scheduled-tasks')
});
