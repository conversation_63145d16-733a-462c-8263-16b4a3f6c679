const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const { shell } = require('electron');
const { exec } = require('child_process');
const util = require('util');
const os = require('os');
const Logger = require('./logger');

const execAsync = util.promisify(exec);

class FileManager {
  constructor() {
    this.isProcessing = false;
    this.isCancelled = false;
    this.logger = new Logger();
  }



  async getFiles(folderPath, filterType, filterParams = {}) {
    try {
      if (!folderPath || !fsSync.existsSync(folderPath)) {
        throw new Error('Invalid folder path');
      }

      console.log(`Scanning folder: ${folderPath}`);
      console.log(`Filter type: ${filterType}`);
      console.log(`Filter params:`, filterParams);

      const files = await fs.readdir(folderPath);
      const fileDetails = [];

      for (const file of files) {
        const filePath = path.join(folderPath, file);
        const stats = await fs.stat(filePath);

        if (stats.isFile()) {
          fileDetails.push({
            name: file,
            path: filePath,
            size: stats.size,
            modified: stats.mtime
          });
        }
      }

      console.log(`Found ${fileDetails.length} files total`);
      const filteredFiles = this.filterFiles(fileDetails, filterType, filterParams);
      console.log(`After filtering: ${filteredFiles.length} files match criteria`);

      return filteredFiles;
    } catch (error) {
      console.error('Error in getFiles:', error);
      throw new Error(`Failed to get files: ${error.message}`);
    }
  }

  filterFiles(files, filterType, filterParams = {}) {
    console.log(`Filtering ${files.length} files with method: ${filterType}`);
    console.log('Filter params:', filterParams);

    let result;
    switch (filterType) {
      case 'all':
        result = files;
        break;

      case 'time-based':
        result = this.filterByTimeRange(files, filterParams.amount || 30, filterParams.unit || 'days');
        break;

      case 'contains-text':
        result = this.filterByContainsText(files, filterParams.text || '');
        break;

      case 'by-extension':
        result = this.filterByExtension(files, filterParams.extension || '');
        break;

      case 'exact-filename':
        result = this.filterByExactFilename(files, filterParams.filename || '');
        break;

      // Legacy support for old filter types
      case 'current-month':
        result = this.filterByTimeRange(files, 1, 'months');
        break;

      case 'last-2-months':
        result = this.filterByTimeRange(files, 2, 'months');
        break;

      case 'quarter':
        result = this.filterByTimeRange(files, 3, 'months');
        break;

      case 'custom':
        result = this.filterByExactFilename(files, filterParams.filename || filterParams);
        break;

      default:
        result = files;
    }

    console.log(`Filter result: ${result.length} files matched`);
    if (result.length > 0) {
      console.log('Matched files:', result.map(f => f.name));
    }

    return result;
  }

  filterByMonth(files, months) {
    const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                       'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    const monthPatterns = months.map(month => {
      const monthName = monthNames[month.getMonth()];
      const year = month.getFullYear();
      const pattern = new RegExp(`${monthName}_?${year}|${monthName}${year}`, 'i');
      console.log(`Looking for pattern: ${monthName}_${year} or ${monthName}${year}`);
      return pattern;
    });

    const matchedFiles = files.filter(file => {
      const matches = monthPatterns.some(pattern => {
        const isMatch = pattern.test(file.name);
        if (isMatch) {
          console.log(`File "${file.name}" matches pattern`);
        }
        return isMatch;
      });
      return matches;
    });

    console.log(`Month filter: ${matchedFiles.length} files matched out of ${files.length}`);
    return matchedFiles;
  }

  filterByCustomPattern(files, pattern) {
    if (!pattern) {
      console.log('No custom pattern provided, returning all files');
      return files;
    }

    console.log(`Applying exact filename match: "${pattern}"`);

    // Use exact filename matching only (case-insensitive)
    const matchedFiles = files.filter(file => {
      const isExactMatch = file.name.toLowerCase() === pattern.toLowerCase();
      if (isExactMatch) {
        console.log(`File "${file.name}" matches exact filename`);
      }
      return isExactMatch;
    });

    console.log(`Exact filename filter: ${matchedFiles.length} files matched out of ${files.length}`);

    if (matchedFiles.length === 0) {
      console.log(`No exact match found for "${pattern}". Available files:`, files.map(f => f.name));
    }

    return matchedFiles;
  }

  filterByTimeRange(files, amount, unit) {
    console.log(`Filtering files from last ${amount} ${unit}`);

    const currentDate = new Date();
    const cutoffDate = new Date();

    switch (unit) {
      case 'days':
        cutoffDate.setDate(cutoffDate.getDate() - amount);
        break;
      case 'weeks':
        cutoffDate.setDate(cutoffDate.getDate() - (amount * 7));
        break;
      case 'months':
        cutoffDate.setMonth(cutoffDate.getMonth() - amount);
        break;
      case 'years':
        cutoffDate.setFullYear(cutoffDate.getFullYear() - amount);
        break;
      default:
        cutoffDate.setDate(cutoffDate.getDate() - amount);
    }

    console.log(`Cutoff date: ${cutoffDate.toISOString()}`);

    const matchedFiles = files.filter(file => {
      const fileDate = new Date(file.modified);
      const isWithinRange = fileDate >= cutoffDate;
      if (isWithinRange) {
        console.log(`File "${file.name}" modified ${fileDate.toISOString()} is within range`);
      }
      return isWithinRange;
    });

    console.log(`Time range filter: ${matchedFiles.length} files matched out of ${files.length}`);
    return matchedFiles;
  }

  filterByContainsText(files, text) {
    if (!text) {
      console.log('No text provided, returning all files');
      return files;
    }

    console.log(`Filtering files containing text: "${text}"`);

    const matchedFiles = files.filter(file => {
      const containsText = file.name.toLowerCase().includes(text.toLowerCase());
      if (containsText) {
        console.log(`File "${file.name}" contains text "${text}"`);
      }
      return containsText;
    });

    console.log(`Contains text filter: ${matchedFiles.length} files matched out of ${files.length}`);
    return matchedFiles;
  }

  filterByExtension(files, extension) {
    if (!extension) {
      console.log('No extension provided, returning all files');
      return files;
    }

    // Ensure extension starts with a dot
    const ext = extension.startsWith('.') ? extension : '.' + extension;
    console.log(`Filtering files with extension: "${ext}"`);

    const matchedFiles = files.filter(file => {
      const hasExtension = file.name.toLowerCase().endsWith(ext.toLowerCase());
      if (hasExtension) {
        console.log(`File "${file.name}" has extension "${ext}"`);
      }
      return hasExtension;
    });

    console.log(`Extension filter: ${matchedFiles.length} files matched out of ${files.length}`);
    return matchedFiles;
  }

  filterByExactFilename(files, filename) {
    if (!filename) {
      console.log('No filename provided, returning all files');
      return files;
    }

    console.log(`Applying exact filename match: "${filename}"`);

    const matchedFiles = files.filter(file => {
      const isExactMatch = file.name.toLowerCase() === filename.toLowerCase();
      if (isExactMatch) {
        console.log(`File "${file.name}" matches exact filename`);
      }
      return isExactMatch;
    });

    console.log(`Exact filename filter: ${matchedFiles.length} files matched out of ${files.length}`);

    if (matchedFiles.length === 0) {
      console.log(`No exact match found for "${filename}". Available files:`, files.map(f => f.name));
    }

    return matchedFiles;
  }

  getFilterDescription(filterType, filterParams = {}) {
    switch (filterType) {
      case 'all':
        return 'All files in folder';
      case 'time-based':
        return `Files from last ${filterParams.amount || 30} ${filterParams.unit || 'days'}`;
      case 'contains-text':
        return filterParams.text ? `Files containing "${filterParams.text}"` : 'Files containing text';
      case 'by-extension':
        return filterParams.extension ? `Files with extension ${filterParams.extension}` : 'Files by extension';
      case 'exact-filename':
        return filterParams.filename ? `Exact filename: "${filterParams.filename}"` : 'Exact filename match';
      default:
        return filterType;
    }
  }

  async processFiles(files, delay, progressCallback, folderPath = '', filterType = '', filterParams = {}) {
    if (this.isProcessing) {
      throw new Error('Processing already in progress');
    }

    this.isProcessing = true;
    this.isCancelled = false;
    const results = [];

    // Get log ID for final logging
    const filterDescription = this.getFilterDescription(filterType, filterParams);
    const logId = await this.logger.logManualProcessing(folderPath, filterType, files.length, filterDescription);

    try {
      for (let i = 0; i < files.length; i++) {
        // Check for cancellation before processing each file
        if (this.isCancelled) {
          console.log('Processing cancelled by user');
          await this.logger.cancelActivity(logId, {
            successCount: results.filter(r => r.status === 'success').length,
            processedFiles: results.length,
            totalFiles: files.length
          });

          // Send cancellation status to UI
          progressCallback({
            current: i + 1,
            total: files.length,
            percentage: Math.round(((i + 1) / files.length) * 100),
            currentFile: 'Processing cancelled',
            status: 'cancelled',
            step: 'Cancelled by user'
          });

          return {
            success: false,
            cancelled: true,
            results
          };
        }

        const file = files[i];

        // Update progress - Opening file
        progressCallback({
          current: i + 1,
          total: files.length,
          percentage: Math.round(((i + 1) / files.length) * 100),
          currentFile: file.name,
          status: 'opening',
          step: 'Opening file...'
        });

        try {
          await this.processFile(file.path, delay, (stepInfo) => {
            // Check for cancellation during file processing
            if (this.isCancelled) {
              throw new Error('Processing cancelled by user');
            }

            progressCallback({
              current: i + 1,
              total: files.length,
              percentage: Math.round(((i + 1) / files.length) * 100),
              currentFile: file.name,
              status: 'processing',
              stepInfo: stepInfo
            });
          });

          results.push({
            file: file.name,
            status: 'success',
            message: 'File opened, refreshed, saved, and closed successfully'
          });
        } catch (error) {
          if (this.isCancelled || error.message.includes('Processing cancelled by user')) {
            console.log('Processing cancelled during file processing');
            await this.logger.cancelActivity(logId, {
              successCount: results.filter(r => r.status === 'success').length,
              processedFiles: results.length,
              totalFiles: files.length
            });

            // Send cancellation status to UI
            progressCallback({
              current: i + 1,
              total: files.length,
              percentage: Math.round(((i + 1) / files.length) * 100),
              currentFile: 'Processing cancelled',
              status: 'cancelled',
              step: 'Cancelled by user'
            });

            return {
              success: false,
              cancelled: true,
              results
            };
          }

          results.push({
            file: file.name,
            status: 'error',
            message: error.message
          });
        }
      }

      // Complete logging
      const successCount = results.filter(r => r.status === 'success').length;
      const errorCount = results.filter(r => r.status === 'error').length;

      await this.logger.completeProcessing(logId, 'manual', 'file-processing', successCount, errorCount, results, {
        folderPath: folderPath,
        filterType: filterType,
        filterDescription: filterDescription,
        fileCount: files.length
      });

      return {
        success: true,
        results
      };
    } catch (error) {
      // Log the error
      await this.logger.failActivity(logId, 'manual', 'file-processing', error.message, {
        folderPath: folderPath,
        filterType: filterType,
        filterDescription: filterDescription,
        fileCount: files.length
      });
      throw error;
    } finally {
      this.isProcessing = false;
      this.isCancelled = false;
    }
  }

  cancelProcessing() {
    console.log('Cancel processing requested');
    this.isCancelled = true;
  }

  async processFile(filePath, delay, stepCallback) {
    try {
      const fileName = path.basename(filePath);
      console.log(`Starting to process file: ${fileName}`);

      // Step 1: Open the file
      stepCallback({
        step: 1,
        totalSteps: 5,
        phase: 'Opening File',
        message: 'Opening file in application...',
        countdown: null
      });

      const openResult = await shell.openPath(filePath);
      console.log(`File open result: ${openResult}`);

      // Wait for application to load
      await this.sleepWithCountdown(3, (remaining) => {
        stepCallback({
          step: 1,
          totalSteps: 5,
          phase: 'Opening File',
          message: 'Waiting for application to load...',
          countdown: remaining
        });
      });

      // Find the window that contains our file
      const windowInfo = await this.findFileWindow(fileName);
      if (windowInfo) {
        console.log(`Found window for ${fileName}:`, windowInfo);
      } else {
        console.log(`Could not find specific window for ${fileName}, will use fallback methods`);
      }

      // Step 2: Wait for auto-refresh
      await this.sleepWithCountdown(delay, (remaining) => {
        stepCallback({
          step: 2,
          totalSteps: 5,
          phase: 'Auto-Refresh',
          message: 'Waiting for data to refresh...',
          countdown: remaining
        });
      });

      // Step 3: Save the file
      stepCallback({
        step: 3,
        totalSteps: 5,
        phase: 'Saving File',
        message: 'Sending save command...',
        countdown: null
      });

      console.log('Sending Ctrl+S to save file...');
      const saveSuccess = await this.sendKeyboardShortcut('ctrl+s', windowInfo);

      if (saveSuccess) {
        console.log('Save command sent successfully');
        await this.sleepWithCountdown(2, (remaining) => {
          stepCallback({
            step: 3,
            totalSteps: 5,
            phase: 'Saving File',
            message: 'Waiting for save to complete...',
            countdown: remaining
          });
        });
      } else {
        console.log('Save command may have failed');
        await this.sleepWithCountdown(2, (remaining) => {
          stepCallback({
            step: 3,
            totalSteps: 5,
            phase: 'Saving File',
            message: 'Save command sent (please verify manually)...',
            countdown: remaining
          });
        });
      }

      // Step 4: Close the file
      stepCallback({
        step: 4,
        totalSteps: 5,
        phase: 'Closing File',
        message: 'Sending close command...',
        countdown: null
      });

      console.log('Sending close command (Ctrl+W)...');
      const closeSuccess = await this.sendKeyboardShortcut('close', windowInfo);

      if (closeSuccess) {
        console.log('Close command sent successfully');

        // Wait for save dialog
        await this.sleepWithCountdown(2, (remaining) => {
          stepCallback({
            step: 4,
            totalSteps: 5,
            phase: 'Closing File',
            message: 'Waiting for save dialog...',
            countdown: remaining
          });
        });

        // Handle save dialog
        stepCallback({
          step: 5,
          totalSteps: 5,
          phase: 'Save Dialog',
          message: 'Clicking Save in dialog...',
          countdown: null
        });

        await this.sendKeyboardShortcut('save-dialog', windowInfo);

        await this.sleepWithCountdown(1, (remaining) => {
          stepCallback({
            step: 5,
            totalSteps: 5,
            phase: 'Save Dialog',
            message: 'Processing save dialog...',
            countdown: remaining
          });
        });

        // Confirm save
        stepCallback({
          step: 5,
          totalSteps: 5,
          phase: 'Save Dialog',
          message: 'Confirming save...',
          countdown: null
        });

        await this.sendKeyboardShortcut('confirm-save', windowInfo);

        stepCallback({
          step: 5,
          totalSteps: 5,
          phase: 'Complete',
          message: 'File processed successfully!',
          countdown: null
        });

        await this.sleep(1000);
      } else {
        console.log('Close command may have failed');
        await this.sleepWithCountdown(3, (remaining) => {
          stepCallback({
            step: 4,
            totalSteps: 5,
            phase: 'Manual Intervention',
            message: 'Please manually close and save the file...',
            countdown: remaining
          });
        });
      }

      console.log(`Completed processing file: ${fileName}`);

    } catch (error) {
      console.error(`Error processing file ${path.basename(filePath)}:`, error);

      // If it's a cancellation error, re-throw it as is
      if (error.message.includes('Processing cancelled by user')) {
        throw error;
      }

      throw new Error(`Failed to process file ${path.basename(filePath)}: ${error.message}`);
    }
  }

  async findFileWindow(fileName) {
    try {
      console.log(`Finding window for file: ${fileName}`);

      if (os.platform() === 'win32') {
        return await this.findWindowsFileWindow(fileName);
      } else if (os.platform() === 'darwin') {
        return await this.findMacFileWindow(fileName);
      } else {
        return await this.findLinuxFileWindow(fileName);
      }
    } catch (error) {
      console.log(`Could not find window for ${fileName}:`, error.message);
      return null;
    }
  }

  async findWindowsFileWindow(fileName) {
    try {
      // Use PowerShell to find the window handle and process info
      const psCommand = `
        Add-Type @"
          using System;
          using System.Runtime.InteropServices;
          using System.Text;
          using System.Diagnostics;
          public class Win32 {
            [DllImport("user32.dll")]
            public static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);
            [DllImport("user32.dll")]
            public static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);
            [DllImport("user32.dll")]
            public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);
            [DllImport("user32.dll")]
            public static extern bool IsWindowVisible(IntPtr hWnd);
            public delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);
          }
"@

        $result = $null
        $callback = {
          param($hwnd, $lParam)
          $title = New-Object System.Text.StringBuilder 256
          [Win32]::GetWindowText($hwnd, $title, $title.Capacity)
          $windowTitle = $title.ToString()

          if ($windowTitle -like "*${fileName}*" -and [Win32]::IsWindowVisible($hwnd)) {
            $processId = 0
            [Win32]::GetWindowThreadProcessId($hwnd, [ref]$processId)
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
              Write-Output "HWND:$hwnd|PID:$processId|PROCESS:$($process.ProcessName)|TITLE:$windowTitle"
              return $false  # Stop enumeration
            }
          }
          return $true
        }

        [Win32]::EnumWindows($callback, [IntPtr]::Zero)
      `;

      const result = await execAsync(`powershell -Command "${psCommand.replace(/"/g, '\\"')}"`);

      if (result.stdout && result.stdout.trim()) {
        const windowInfo = result.stdout.trim();
        const parts = windowInfo.split('|');
        const hwnd = parts[0].split(':')[1];
        const pid = parts[1].split(':')[1];
        const processName = parts[2].split(':')[1];
        const title = parts[3].split(':')[1];

        console.log(`Found window - HWND: ${hwnd}, PID: ${pid}, Process: ${processName}, Title: ${title}`);

        return {
          hwnd: hwnd,
          pid: parseInt(pid),
          processName: processName,
          title: title
        };
      }

      return null;
    } catch (error) {
      console.log(`Failed to find Windows file window:`, error.message);
      return null;
    }
  }

  async findMacFileWindow(fileName) {
    try {
      // macOS implementation - find the application that has the file open
      const result = await execAsync(`osascript -e 'tell application "System Events" to get name of every process whose visible is true'`);
      // This is a simplified approach - in practice, you'd need more sophisticated window detection
      return { processName: 'Unknown', title: fileName };
    } catch (error) {
      console.log(`Failed to find Mac file window:`, error.message);
      return null;
    }
  }

  async findLinuxFileWindow(fileName) {
    try {
      // Linux implementation using wmctrl
      const result = await execAsync(`wmctrl -l | grep "${fileName}"`);
      if (result.stdout && result.stdout.trim()) {
        const windowInfo = result.stdout.trim().split(/\s+/);
        return {
          windowId: windowInfo[0],
          title: windowInfo.slice(3).join(' ')
        };
      }
      return null;
    } catch (error) {
      console.log(`Failed to find Linux file window:`, error.message);
      return null;
    }
  }

  async sendKeyboardShortcut(shortcut, windowInfo = null) {
    try {
      if (os.platform() === 'win32') {
        // Send keyboard shortcut directly to specific window
        return await this.sendWindowsKeyboardShortcut(shortcut, windowInfo);
      } else if (os.platform() === 'darwin') {
        // macOS implementation using osascript
        let appleScript;

        if (shortcut === 'ctrl+s') {
          appleScript = 'tell application "System Events" to keystroke "s" using command down';
        } else if (shortcut === 'close') {
          appleScript = 'tell application "System Events" to keystroke "w" using command down';
        } else if (shortcut === 'save-dialog') {
          // Press Enter to confirm save dialog
          appleScript = 'tell application "System Events" to keystroke return';
        } else if (shortcut === 'alt+f4') {
          // Fallback: Cmd+Q to quit application
          appleScript = 'tell application "System Events" to keystroke "q" using command down';
        }

        if (appleScript) {
          await execAsync(`osascript -e '${appleScript}'`);
          console.log(`Sent keyboard shortcut: ${shortcut}`);
        }
      } else {
        // Linux implementation using xdotool (if available)
        let xdotoolCommand;

        if (shortcut === 'ctrl+s') {
          xdotoolCommand = 'xdotool key ctrl+s';
        } else if (shortcut === 'close') {
          xdotoolCommand = 'xdotool key ctrl+w';
        } else if (shortcut === 'save-dialog') {
          xdotoolCommand = 'xdotool key Return';
        } else if (shortcut === 'alt+f4') {
          xdotoolCommand = 'xdotool key alt+F4';
        }

        if (xdotoolCommand) {
          await execAsync(xdotoolCommand);
          console.log(`Sent keyboard shortcut: ${shortcut}`);
        }
      }

    } catch (error) {
      console.error(`Failed to send keyboard shortcut ${shortcut}:`, error);
      // Don't throw error here as the file might still be processed manually
    }
  }

  async sendMessageToWindow(shortcut, windowInfo) {
    try {
      console.log(`Sending ${shortcut} directly to window HWND: ${windowInfo.hwnd}`);

      const psCommand = `
        Add-Type @"
          using System;
          using System.Runtime.InteropServices;
          public class Win32 {
            [DllImport("user32.dll")]
            public static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);
            [DllImport("user32.dll")]
            public static extern bool PostMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);
            [DllImport("user32.dll")]
            public static extern bool SetForegroundWindow(IntPtr hWnd);
            [DllImport("user32.dll")]
            public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

            public const uint WM_KEYDOWN = 0x0100;
            public const uint WM_KEYUP = 0x0101;
            public const uint WM_CHAR = 0x0102;
            public const uint WM_COMMAND = 0x0111;
            public const uint WM_SYSCOMMAND = 0x0112;

            public const int VK_CONTROL = 0x11;
            public const int VK_S = 0x53;
            public const int VK_W = 0x57;
            public const int VK_RETURN = 0x0D;
          }
"@

        $hwnd = [IntPtr]${windowInfo.hwnd}

        # Ensure window is visible and restored
        [Win32]::ShowWindow($hwnd, 9)  # SW_RESTORE
        Start-Sleep -Milliseconds 100
      `;

      let keyCommand = '';

      if (shortcut === 'ctrl+s') {
        keyCommand = `
          # Send Ctrl+S to the specific window
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYDOWN, [Win32]::VK_CONTROL, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYDOWN, [Win32]::VK_S, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYUP, [Win32]::VK_S, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYUP, [Win32]::VK_CONTROL, 0)
        `;
      } else if (shortcut === 'close') {
        keyCommand = `
          # Send Ctrl+W to the specific window
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYDOWN, [Win32]::VK_CONTROL, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYDOWN, [Win32]::VK_W, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYUP, [Win32]::VK_W, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYUP, [Win32]::VK_CONTROL, 0)
        `;
      } else if (shortcut === 'save-dialog' || shortcut === 'confirm-save') {
        keyCommand = `
          # Send Enter to the specific window
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYDOWN, [Win32]::VK_RETURN, 0)
          [Win32]::PostMessage($hwnd, [Win32]::WM_KEYUP, [Win32]::VK_RETURN, 0)
        `;
      }

      const fullCommand = psCommand + keyCommand;

      await execAsync(`powershell -Command "${fullCommand.replace(/"/g, '\\"')}"`);
      console.log(`Successfully sent ${shortcut} to window ${windowInfo.hwnd}`);
      return true;

    } catch (error) {
      console.log(`Failed to send message to window:`, error.message);
      throw error;
    }
  }

  async sendWindowsKeyboardShortcut(shortcut, windowInfo = null) {
    // Method 1: Send message directly to specific window (if we have window info)
    if (windowInfo && windowInfo.hwnd) {
      try {
        return await this.sendMessageToWindow(shortcut, windowInfo);
      } catch (error) {
        console.log(`Direct window message failed for ${shortcut}, trying fallback...`);
      }
    }

    // Method 2: Fallback to global keyboard shortcuts
    try {
      let psCommand;

      if (shortcut === 'ctrl+s') {
        psCommand = `Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('^s')`;
      } else if (shortcut === 'close') {
        // Use Ctrl+W to close document (better than Alt+F4)
        psCommand = `Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('^w')`;
      } else if (shortcut === 'save-dialog') {
        // Press Enter to click "Save" button in save dialog (usually the default)
        psCommand = `Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('{ENTER}')`;
      } else if (shortcut === 'confirm-save') {
        // Try multiple ways to confirm save: Alt+S for Save button, then Alt+Y for Yes
        psCommand = `Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%s'); Start-Sleep -Milliseconds 500; [System.Windows.Forms.SendKeys]::SendWait('%y')`;
      } else if (shortcut === 'alt+f4') {
        // Keep Alt+F4 as fallback
        psCommand = `Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%{F4}')`;
      }

      if (psCommand) {
        await execAsync(`powershell -Command "${psCommand}"`);
        console.log(`Sent keyboard shortcut via PowerShell: ${shortcut}`);
        return true;
      }
    } catch (error) {
      console.log(`PowerShell method failed for ${shortcut}, trying alternative...`);
    }

    // Method 2: Try VBScript approach
    try {
      const fs = require('fs');
      const path = require('path');
      const tempDir = require('os').tmpdir();
      const vbsFile = path.join(tempDir, 'sendkeys.vbs');

      let vbsContent;
      if (shortcut === 'ctrl+s') {
        vbsContent = `
          Set WshShell = WScript.CreateObject("WScript.Shell")
          WshShell.SendKeys "^s"
        `;
      } else if (shortcut === 'close') {
        vbsContent = `
          Set WshShell = WScript.CreateObject("WScript.Shell")
          WshShell.SendKeys "^w"
        `;
      } else if (shortcut === 'save-dialog') {
        vbsContent = `
          Set WshShell = WScript.CreateObject("WScript.Shell")
          WshShell.SendKeys "{ENTER}"
        `;
      } else if (shortcut === 'confirm-save') {
        vbsContent = `
          Set WshShell = WScript.CreateObject("WScript.Shell")
          WshShell.SendKeys "%s"
          WScript.Sleep 500
          WshShell.SendKeys "%y"
        `;
      } else if (shortcut === 'alt+f4') {
        vbsContent = `
          Set WshShell = WScript.CreateObject("WScript.Shell")
          WshShell.SendKeys "%{F4}"
        `;
      }

      if (vbsContent) {
        fs.writeFileSync(vbsFile, vbsContent.trim());
        await execAsync(`cscript //nologo "${vbsFile}"`);
        fs.unlinkSync(vbsFile); // Clean up
        console.log(`Sent keyboard shortcut via VBScript: ${shortcut}`);
        return true;
      }
    } catch (error) {
      console.log(`VBScript method failed for ${shortcut}:`, error.message);
    }

    return false;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  sleepWithCountdown(seconds, updateCallback) {
    return new Promise((resolve, reject) => {
      let remaining = seconds;

      // Check for cancellation before starting
      if (this.isCancelled) {
        reject(new Error('Processing cancelled by user'));
        return;
      }

      // Call immediately with initial value
      updateCallback(remaining);

      const interval = setInterval(() => {
        // Check for cancellation during countdown
        if (this.isCancelled) {
          clearInterval(interval);
          reject(new Error('Processing cancelled by user'));
          return;
        }

        remaining--;
        if (remaining > 0) {
          updateCallback(remaining);
        } else {
          clearInterval(interval);
          resolve();
        }
      }, 1000);
    });
  }



  isCurrentlyProcessing() {
    return this.isProcessing;
  }
}

module.exports = FileManager;
