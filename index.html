<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoRefresh - File Management & Scheduling</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1>AutoRefresh</h1>
            <p>Automated File Refresh & Scheduling Tool</p>
        </header>

        <nav class="tab-navigation">
            <button class="tab-btn active" data-tab="logs">Activity Logs</button>
            <button class="tab-btn" data-tab="file-manager">File Manager</button>
            <button class="tab-btn" data-tab="scheduler">Task Scheduler</button>
            <button class="tab-btn" data-tab="settings">Settings</button>
        </nav>

        <!-- Logs Tab -->
        <div id="logs" class="tab-content active">
            <div class="section">
                <h2>Activity Logs</h2>

                <div class="logs-controls">
                    <div class="logs-filters">
                        <select id="log-type-filter">
                            <option value="">All Activities</option>
                            <option value="manual">Manual Processing</option>
                            <option value="scheduled">Scheduled Tasks</option>
                        </select>

                        <select id="log-status-filter">
                            <option value="">All Status</option>
                            <option value="successful">Successful</option>
                            <option value="failed">Failed</option>
                        </select>

                        <button id="refresh-logs-btn" class="btn btn-secondary">Refresh</button>
                        <button id="clear-logs-btn" class="btn btn-danger">Clear All Logs</button>
                    </div>

                    <div class="logs-stats" id="logs-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total:</span>
                            <span class="stat-value" id="stat-total">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Success Rate:</span>
                            <span class="stat-value" id="stat-success-rate">0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Manual:</span>
                            <span class="stat-value" id="stat-manual">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Scheduled:</span>
                            <span class="stat-value" id="stat-scheduled">0</span>
                        </div>
                    </div>
                </div>

                <div id="logs-list" class="logs-list">
                    <p class="placeholder">Loading activity logs...</p>
                </div>
            </div>
        </div>

        <!-- File Manager Tab -->
        <div id="file-manager" class="tab-content">
            <div class="section">
                <h2>Folder Selection</h2>
                <div class="folder-selection">
                    <button id="select-folder-btn" class="btn btn-primary">Select Folder</button>
                    <span id="selected-folder" class="folder-path">No folder selected</span>
                </div>
            </div>

            <div class="section">
                <h2>Update Method</h2>
                <div class="update-methods">
                    <label class="radio-option">
                        <input type="radio" name="updateMethod" value="all" checked>
                        <span>All files in folder</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="updateMethod" value="time-based">
                        <span>Files from last</span>
                        <div class="inline-inputs">
                            <input type="number" id="time-amount" min="1" max="999" value="30">
                            <select id="time-unit">
                                <option value="days">days</option>
                                <option value="weeks">weeks</option>
                                <option value="months">months</option>
                                <option value="years">years</option>
                            </select>
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="updateMethod" value="contains-text">
                        <span>Files containing</span>
                        <div class="inline-inputs">
                            <input type="text" id="contains-text" placeholder="Enter text to search">
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="updateMethod" value="by-extension">
                        <span>Files with extension</span>
                        <div class="inline-inputs">
                            <select id="file-extension">
                                <option value=".xlsx">.xlsx</option>
                                <option value=".pdf">.pdf</option>
                                <option value=".docx">.docx</option>
                                <option value=".txt">.txt</option>
                                <option value=".csv">.csv</option>
                                <option value=".pptx">.pptx</option>
                                <option value="custom">Custom...</option>
                            </select>
                            <input type="text" id="custom-extension" placeholder=".ext" style="display: none;">
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="updateMethod" value="exact-filename">
                        <span>Exact filename match</span>
                    </label>
                </div>

                <div id="exact-filename-input" class="custom-input" style="display: none;">
                    <input type="text" id="exact-filename" placeholder="Enter exact filename (e.g., Report_JAN_2025.xlsx)">
                </div>
            </div>

            <div class="section">
                <h2>Processing Settings</h2>
                <div class="settings-row">
                    <label for="delay-input">Refresh Delay (seconds):</label>
                    <input type="number" id="delay-input" value="5" min="1">
                </div>
            </div>

            <div class="section">
                <h2>File Preview</h2>
                <div id="file-list" class="file-list">
                    <p class="placeholder">Select a folder to automatically preview files</p>
                </div>
            </div>

            <div class="section">
                <div class="action-buttons">
                    <button id="process-files-btn" class="btn btn-success" disabled>Start Processing</button>
                </div>
            </div>

            <div id="progress-section" class="section" style="display: none;">
                <h2>Processing Progress</h2>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <div id="progress-text" class="progress-text">0%</div>
                </div>
                <div id="current-file" class="current-file"></div>
                <div class="action-buttons" style="margin-top: 1rem;">
                    <button id="cancel-processing-btn" class="btn btn-danger">Cancel Processing</button>
                </div>
            </div>
        </div>

        <!-- Scheduler Tab -->
        <div id="scheduler" class="tab-content">
            <div class="section">
                <h2>Task Name</h2>
                <div class="task-name-input">
                    <input type="text" id="task-name" placeholder="Enter task name" class="folder-path">
                </div>
            </div>

            <div class="section">
                <h2>Folder Selection</h2>
                <div class="folder-selection">
                    <button id="task-select-folder-btn" class="btn btn-primary">Select Folder</button>
                    <span id="task-selected-folder" class="folder-path">No folder selected</span>
                </div>
            </div>

            <div class="section">
                <h2>Update Method</h2>
                <div class="update-methods">
                    <label class="radio-option">
                        <input type="radio" name="taskUpdateMethod" value="all" checked>
                        <span>All files in folder</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="taskUpdateMethod" value="time-based">
                        <span>Files from last</span>
                        <div class="inline-inputs">
                            <input type="number" id="task-time-amount" min="1" max="999" value="30">
                            <select id="task-time-unit">
                                <option value="days">days</option>
                                <option value="weeks">weeks</option>
                                <option value="months">months</option>
                                <option value="years">years</option>
                            </select>
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="taskUpdateMethod" value="contains-text">
                        <span>Files containing</span>
                        <div class="inline-inputs">
                            <input type="text" id="task-contains-text" placeholder="Enter text to search">
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="taskUpdateMethod" value="by-extension">
                        <span>Files with extension</span>
                        <div class="inline-inputs">
                            <select id="task-file-extension">
                                <option value=".xlsx">.xlsx</option>
                                <option value=".pdf">.pdf</option>
                                <option value=".docx">.docx</option>
                                <option value=".txt">.txt</option>
                                <option value=".csv">.csv</option>
                                <option value=".pptx">.pptx</option>
                                <option value="custom">Custom...</option>
                            </select>
                            <input type="text" id="task-custom-extension" placeholder=".ext" style="display: none;">
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="taskUpdateMethod" value="exact-filename">
                        <span>Exact filename match</span>
                    </label>
                </div>

                <div id="task-exact-filename-input" class="custom-input" style="display: none;">
                    <input type="text" id="task-exact-filename" placeholder="Enter exact filename (e.g., Report_JAN_2025.xlsx)">
                </div>
            </div>

            <div class="section">
                <h2>Processing Settings</h2>
                <div class="settings-row">
                    <label for="task-delay-input">Refresh Delay (seconds):</label>
                    <input type="number" id="task-delay-input" value="5" min="1">
                </div>
            </div>

            <div class="section">
                <h2>File Preview</h2>
                <div id="task-file-list" class="file-list">
                    <p class="placeholder">Select a folder to automatically preview files</p>
                </div>
            </div>

            <div class="section">
                <h2>Schedule Settings</h2>
                <div class="schedule-settings">
                    <div class="schedule-row">
                        <label for="task-frequency">Frequency:</label>
                        <select id="task-frequency">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="custom">Custom Schedule</option>
                        </select>
                    </div>
                    <div class="schedule-row">
                        <label for="task-time">Time:</label>
                        <input type="time" id="task-time" value="09:00">
                    </div>
                </div>

                <div id="task-weekly-input" class="custom-input" style="display: none;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Days of Week:</label>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-monday" value="1" checked>
                            <span>Monday</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-tuesday" value="2" checked>
                            <span>Tuesday</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-wednesday" value="3" checked>
                            <span>Wednesday</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-thursday" value="4" checked>
                            <span>Thursday</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-friday" value="5" checked>
                            <span>Friday</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-saturday" value="6">
                            <span>Saturday</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" id="day-sunday" value="0">
                            <span>Sunday</span>
                        </label>
                    </div>
                </div>

                <div id="task-monthly-input" class="custom-input" style="display: none;">
                    <label for="task-day-of-month" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Day of Month:</label>
                    <select id="task-day-of-month" style="width: 200px; padding: 0.5rem; border: 1px solid #ced4da; border-radius: 4px;">
                        <option value="1">1st</option>
                        <option value="2">2nd</option>
                        <option value="3">3rd</option>
                        <option value="4">4th</option>
                        <option value="5">5th</option>
                        <option value="10">10th</option>
                        <option value="15">15th</option>
                        <option value="20">20th</option>
                        <option value="25">25th</option>
                        <option value="last">Last day</option>
                    </select>
                </div>

                <div id="task-custom-input" class="custom-input" style="display: none;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Custom Schedule:</label>
                    <div class="custom-schedule">
                        <div class="schedule-item">
                            <label for="custom-minute">Minute:</label>
                            <select id="custom-minute">
                                <option value="0">00</option>
                                <option value="15">15</option>
                                <option value="30">30</option>
                                <option value="45">45</option>
                            </select>
                        </div>
                        <div class="schedule-item">
                            <label for="custom-hour">Hour:</label>
                            <select id="custom-hour">
                                <option value="9">9 AM</option>
                                <option value="12">12 PM</option>
                                <option value="15">3 PM</option>
                                <option value="18">6 PM</option>
                                <option value="*">Every hour</option>
                            </select>
                        </div>
                        <div class="schedule-item">
                            <label for="custom-day">Day:</label>
                            <select id="custom-day">
                                <option value="*">Every day</option>
                                <option value="1-5">Weekdays</option>
                                <option value="6,0">Weekends</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="action-buttons">
                    <button id="create-task-btn" class="btn btn-success" disabled>Create Task</button>
                    <button id="cancel-edit-btn" class="btn btn-secondary" style="display: none;">Cancel Edit</button>
                </div>
            </div>

            <div class="section">
                <h2>Scheduled Tasks</h2>
                <div id="tasks-list" class="tasks-list">
                    <p class="placeholder">No scheduled tasks</p>
                </div>

                <!-- Progress Section for Scheduled Tasks -->
                <div id="scheduler-progress-section" class="progress-section" style="display: none;">
                    <h3>Task Progress</h3>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div id="scheduler-progress-fill" class="progress-fill"></div>
                        </div>
                        <div id="scheduler-progress-text" class="progress-text">0%</div>
                    </div>
                    <div id="scheduler-current-file" class="current-file">
                        No task running
                    </div>
                    <div class="action-buttons" style="margin-top: 1rem;">
                        <button id="cancel-scheduler-btn" class="btn btn-danger">Cancel Task</button>
                    </div>
                </div>
            </div>
        </div>



        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="section">
                <h2>Application Settings</h2>

                <div class="settings-group">
                    <h3>Startup Options</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="startup-checkbox">
                            <span class="checkmark"></span>
                            Run AutoRefresh when Windows starts
                        </label>
                        <p class="setting-description">
                            Automatically start AutoRefresh when you log into Windows.
                            Scheduled tasks will run in the background.
                        </p>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Notifications</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="notifications-checkbox" checked>
                            <span class="checkmark"></span>
                            Show desktop notifications
                        </label>
                        <p class="setting-description">
                            Display notifications when file processing starts and completes.
                        </p>
                    </div>

                    <div class="setting-item">
                        <button id="test-notification-btn" class="btn btn-secondary">Test Notification</button>
                        <p class="setting-description">
                            Click to test if notifications are working properly.
                        </p>
                    </div>

                    <div class="setting-item">
                        <button id="debug-tasks-btn" class="btn btn-secondary">Debug Scheduled Tasks</button>
                        <p class="setting-description">
                            Check console for scheduled task debugging information.
                        </p>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
