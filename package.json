{"name": "autorefresh-app", "version": "1.0.0", "description": "Automated file refresh application with scheduling", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "file-refresh", "automation", "scheduler"], "author": "AutoRefresh Team", "license": "MIT", "dependencies": {"node-cron": "^3.0.3"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "build": {"appId": "com.autorefresh.app", "productName": "AutoRefresh", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "type": "commonjs"}