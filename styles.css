* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.app-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.app-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.tab-navigation {
    display: flex;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.tab-btn {
    flex: 1;
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: #dee2e6;
}

.tab-btn.active {
    background: white;
    border-bottom: 3px solid #667eea;
    color: #667eea;
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

.section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.section h2 {
    margin-bottom: 1rem;
    color: #495057;
    font-size: 1.3rem;
}

.folder-selection {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.folder-path {
    font-family: monospace;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    border: 1px solid #ced4da;
    flex: 1;
}

.task-name-input {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.task-name-input input {
    width: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.update-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.radio-option {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.75rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
    min-height: 50px;
}

.radio-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.radio-option input[type="radio"] {
    margin: 0;
    flex-shrink: 0;
}

.radio-option > span {
    flex-shrink: 0;
    font-weight: 500;
    color: #495057;
}

.inline-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-left: 0.5rem;
}

.inline-inputs input,
.inline-inputs select {
    padding: 0.4rem 0.6rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
    min-width: 60px;
    height: 32px;
    box-sizing: border-box;
}

.inline-inputs input[type="number"] {
    width: 70px;
    text-align: center;
}

.inline-inputs input[type="text"] {
    min-width: 140px;
    max-width: 160px;
}

.inline-inputs select {
    min-width: 90px;
}

.inline-inputs input:focus,
.inline-inputs select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.inline-inputs input:disabled,
.inline-inputs select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .update-methods {
        grid-template-columns: 1fr;
    }

    .radio-option {
        flex-wrap: wrap;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .inline-inputs {
        margin-left: 0.5rem;
        justify-content: flex-start;
    }

    .inline-inputs input[type="text"] {
        min-width: 120px;
        max-width: 140px;
    }
}

.custom-input {
    margin-top: 1rem;
}

.custom-input input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.settings-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.settings-row label {
    font-weight: 500;
    min-width: 150px;
}

.settings-row input {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    width: 100px;
}

.schedule-settings {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.schedule-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.schedule-row label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.schedule-row select,
.schedule-row input {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    background: white;
}

.schedule-row select:focus,
.schedule-row input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

@media (max-width: 768px) {
    .schedule-settings {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.file-list {
    max-height: 300px;
    overflow-y: auto;
    background: white;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 1rem;
}

.file-item {
    padding: 0.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-item:last-child {
    border-bottom: none;
}

.file-name {
    font-family: monospace;
    font-size: 0.9rem;
}

.file-size {
    color: #6c757d;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.progress-container {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-weight: 500;
}

.current-file {
    font-family: monospace;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.scheduler-form {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-row {
    margin-bottom: 1rem;
}

.form-row label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-row input,
.form-row select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.folder-input {
    display: flex;
    gap: 0.5rem;
}

.folder-input input {
    flex: 1;
}

.tasks-list {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.task-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-item:last-child {
    border-bottom: none;
}

.task-info h4 {
    margin-bottom: 0.25rem;
    color: #495057;
}

.task-info p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0.25rem 0;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.placeholder {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

small {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Settings Tab Styles */
.settings-group {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.settings-group h3 {
    margin-bottom: 1rem;
    color: #495057;
    font-size: 1.1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

.setting-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    padding-left: 2rem;
}



/* Custom checkbox styling */
.checkmark {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #ced4da;
    border-radius: 3px;
    margin-right: 0.75rem;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
}

/* Logs Tab Styles */
.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.logs-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.logs-filters select {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.logs-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.logs-list {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    max-height: 500px;
    overflow-y: auto;
}

.log-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.log-item:hover {
    background: #f8f9fa;
}

.log-item:last-child {
    border-bottom: none;
}

.log-info {
    flex: 1;
}

.log-info h4 {
    margin-bottom: 0.25rem;
    color: #495057;
    font-size: 1rem;
}

.log-info p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0.25rem 0;
}

.log-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: monospace;
}

.log-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}



.status-completed_with_errors {
    background: #ffeaa7;
    color: #d63031;
}

.status-started {
    background: #cce5ff;
    color: #004085;
}

.log-type {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.5rem;
}

.type-manual {
    background: #e3f2fd;
    color: #1565c0;
}

.type-scheduled {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* Step Indicator Styles */
.step-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e9ecef;
    border: 2px solid #ced4da;
    transition: all 0.3s ease;
}

.step-indicator.active {
    background: #667eea;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.step-indicator.complete {
    background: #28a745;
    border-color: #28a745;
}

/* Scheduling Interface Styles */
.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.checkbox-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 0.5rem;
}

.checkbox-item input[type="checkbox"]:checked + span {
    font-weight: 500;
    color: #667eea;
}

.custom-schedule {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.schedule-item {
    display: flex;
    flex-direction: column;
}

.schedule-item label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: #495057;
}

.schedule-item select {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Task Action Buttons */
.task-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.2;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    text-decoration: none;
    font-weight: 500;
}

.btn-small:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-small:active {
    transform: translateY(0);
}

.btn-small span {
    font-size: 0.8rem;
}

.btn-primary.btn-small {
    background: #667eea;
    color: white;
}

.btn-primary.btn-small:hover {
    background: #5a6fd8;
}

.btn-danger.btn-small {
    background: #dc3545;
    color: white;
}

.btn-danger.btn-small:hover {
    background: #c82333;
}

.btn-secondary.btn-small {
    background: #6c757d;
    color: white;
}

.btn-secondary.btn-small:hover {
    background: #5a6268;
}

.btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-small:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Form Actions */
.form-row button {
    margin-right: 0.5rem;
}

.form-row button:last-child {
    margin-right: 0;
}
